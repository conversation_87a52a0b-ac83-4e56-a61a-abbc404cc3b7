{"openapi": "3.0.0", "paths": {"/api/v1": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}}}, "/api/v1/system/config": {"get": {"operationId": "SystemConfigController_findAll", "summary": "Get all system configs", "parameters": [], "responses": {"200": {"description": "Return all system configs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["system"]}}, "/api/v1/system/config/{key}": {"get": {"operationId": "SystemConfigController_findByKey", "summary": "Get system config by key", "parameters": [{"name": "key", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return system config by key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["system"]}, "put": {"operationId": "SystemConfigController_update", "summary": "Update system config by key (Admin only)", "parameters": [{"name": "key", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "System config updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["system"], "security": [{"bearer": []}]}}, "/api/v1/users/me": {"get": {"operationId": "UsersController_getCurrentUser", "summary": "Get current user information", "parameters": [], "responses": {"200": {"description": "Return current user data successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/profile": {"patch": {"operationId": "UsersController_updateProfile", "summary": "Update user profile", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileDto"}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/kyc": {"patch": {"operationId": "UsersController_updateKyc", "summary": "Update user profile", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateKycDto"}}}}, "responses": {"200": {"description": "Kyc updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/admin/kyc/{walletAddress}": {"patch": {"operationId": "UsersController_updateKycByWalletAddress", "summary": "Update user profile", "parameters": [{"name": "wallet<PERSON>ddress", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateKycDto"}}}}, "responses": {"200": {"description": "Kyc updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/api-key/kyc": {"post": {"operationId": "UsersController_updateKycApiKey", "summary": "Update KYC using API Key", "parameters": [{"name": "x-wallet-address", "in": "header", "description": "Wallet Address", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateKycApiKeyDto"}}}}, "responses": {"200": {"description": "Update KYC successful"}}, "tags": ["users"]}}, "/api/v1/users/tree": {"get": {"operationId": "UsersController_getTreeData", "summary": "Get affiliate tree data including stats and direct referrals", "parameters": [], "responses": {"200": {"description": "Return tree data successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TreeResponseDto"}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/{id}": {"get": {"operationId": "UsersController_findOne", "summary": "Get user by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return user data successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/admin/search": {"get": {"operationId": "UsersController_searchUser", "summary": "Search user by wallet address or referral code (admin only)", "parameters": [{"name": "query", "required": true, "in": "query", "description": "Wallet address or referral code to search", "schema": {"type": "string"}}], "responses": {"200": {"description": "User found successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/admin/{userId}/balance": {"patch": {"operationId": "UsersController_updateUserBalance", "summary": "Update user balance (admin only)", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserBalanceDto"}}}}, "responses": {"200": {"description": "User balance updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/make-admin": {"post": {"operationId": "UsersController_makeCurrentUserAdmin", "summary": "Make current user admin (debug only)", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/tusers/find-deep-user": {"get": {"operationId": "FindDeepUserController_findDeepUser", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["tusers"]}}, "/api/v1/tusers/check-referral-path/{userId}": {"get": {"operationId": "FindDeepUserController_checkReferralPath", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["tusers"]}}, "/api/v1/wallet/default": {"get": {"operationId": "WalletController_getDefaultWallet", "summary": "Get default wallet information", "parameters": [], "responses": {"200": {"description": "Return default wallet info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "404": {"description": "Wallet not found"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/me": {"get": {"operationId": "WalletController_getCurrentUserWallet", "summary": "Get current user wallet information", "parameters": [], "responses": {"200": {"description": "Return current user wallet info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "404": {"description": "Wallet not found"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/api-key/me": {"get": {"operationId": "WalletController_getCurrentUserWalletApiKey", "summary": "Get current user wallet information", "parameters": [{"name": "x-wallet-address", "in": "header", "description": "Wallet Address", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return current user wallet info"}, "404": {"description": "Wallet not found"}}, "tags": ["wallet"]}}, "/api/v1/wallet": {"get": {"operationId": "WalletController_findAll", "summary": "Get all wallets", "parameters": [{"name": "isActive", "required": true, "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Return all wallets"}}, "tags": ["wallet"]}}, "/api/v1/wallet/{id}": {"get": {"operationId": "WalletController_findOne", "summary": "Get wallet by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return wallet by ID"}, "404": {"description": "Wallet not found"}}, "tags": ["wallet"]}, "patch": {"operationId": "WalletController_update", "summary": "Update wallet by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWalletDto"}}}}, "responses": {"200": {"description": "Wallet updated successfully"}, "404": {"description": "Wallet not found"}}, "tags": ["wallet"]}, "delete": {"operationId": "WalletController_remove", "summary": "Delete wallet by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Wallet deleted successfully"}}, "tags": ["wallet"]}}, "/api/v1/wallet/{walletId}/transactions": {"get": {"operationId": "WalletController_getTransactions", "summary": "Get transactions by wallet ID", "parameters": [{"name": "walletId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "type", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return transactions by wallet ID"}}, "tags": ["wallet"]}}, "/api/v1/wallet/deposit/usdt": {"post": {"operationId": "WalletController_depositUsdt", "summary": "Deposit USDT", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepositUsdtDto"}}}}, "responses": {"200": {"description": "Deposit USDT created successfully"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/api-key/deposit/usdt": {"post": {"operationId": "WalletController_depositUsdtApiKey", "summary": "Deposit USDT using API Key", "parameters": [{"name": "x-wallet-address", "in": "header", "description": "Wallet Address", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepositUsdtDto"}}}}, "responses": {"200": {"description": "Deposit USDT created successfully"}}, "tags": ["wallet"]}}, "/api/v1/wallet/deposit/wm": {"post": {"operationId": "WalletController_depositWm", "summary": "Deposit WM", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepositUsdtDto"}}}}, "responses": {"200": {"description": "Deposit WM created successfully"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/transactions/history": {"get": {"operationId": "WalletController_getTransactionHistory", "summary": "Get transaction history for the authenticated user", "parameters": [{"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "type", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return transaction history"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/api-key/transactions/history": {"get": {"operationId": "WalletController_getTransactionHistoryApiKey", "summary": "Get transaction history for the authenticated user", "parameters": [{"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "type", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return transaction history"}}, "tags": ["wallet"]}}, "/api/v1/wallet/task/complete": {"post": {"operationId": "WalletController_completeTask", "summary": "Complete a task and receive reward", "parameters": [], "responses": {"200": {"description": "Task completed successfully"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/lightning": {"post": {"operationId": "WalletController_purchaseLightning", "summary": "Create a new Lightning Bolt purchase", "parameters": [], "responses": {"201": {"description": "Lightning Bolt purchase created successfully"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/priority": {"post": {"operationId": "WalletController_purchasePriority", "summary": "Create a new priority purchase", "parameters": [], "responses": {"201": {"description": "Priority purchase created successfully"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/mining": {"post": {"operationId": "WalletController_mineWm", "summary": "Mine WM tokens", "parameters": [], "responses": {"200": {"description": "Mining successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "400": {"description": "Already mined today"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/withdraw/usdt": {"post": {"operationId": "WalletController_withdrawUsdt", "summary": "Withdraw USDT", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WithdrawUsdtDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> successful"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/api-key/withdraw/usdt": {"post": {"operationId": "WalletController_withdrawUsdtApiKey", "summary": "Withdraw USDT using API Key", "parameters": [{"name": "x-wallet-address", "in": "header", "description": "Wallet Address", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WithdrawUsdtDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> successful"}}, "tags": ["wallet"]}}, "/api/v1/wallet/transfer/wm": {"post": {"operationId": "WalletController_transferWm", "summary": "Transfer WM", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferWmDto"}}}}, "responses": {"200": {"description": "Transfer successful"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/api-key/transfer/wm": {"post": {"operationId": "WalletController_transferWmApiKey", "summary": "Transfer WM using API Key", "parameters": [{"name": "x-wallet-address", "in": "header", "description": "Wallet Address", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferWmDto"}}}}, "responses": {"200": {"description": "Transfer successful"}}, "tags": ["wallet"]}}, "/api/v1/wallet/transfer/usdt": {"post": {"operationId": "WalletController_transferUsdt", "summary": "Transfer USDT to another wallet", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferUsdtDto"}}}}, "responses": {"200": {"description": "Transfer successful"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/wallet/api-key/transfer/usdt": {"post": {"operationId": "WalletController_transferUsdtApiKey", "summary": "Transfer USDT using API Key", "parameters": [{"name": "x-wallet-address", "in": "header", "description": "Wallet Address", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferUsdtDto"}}}}, "responses": {"200": {"description": "Transfer successful"}}, "tags": ["wallet"]}}, "/api/v1/wallet/api-key/adjust/wm": {"post": {"operationId": "WalletController_adjustWmApiKey", "summary": "Adjust WM using API Key", "parameters": [{"name": "x-wallet-address", "in": "header", "description": "Wallet Address", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdjustWmDto"}}}}, "responses": {"200": {"description": "Adjust successful"}}, "tags": ["wallet"]}}, "/api/v1/wallet/auto-cancel-pending-withdrawals": {"post": {"operationId": "WalletController_autoCancelPendingWithdrawals", "summary": "Auto-cancel pending withdrawal transactions older than specified hours", "parameters": [{"name": "hours", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "Pending withdrawals auto-cancelled successfully"}}, "tags": ["wallet"], "security": [{"bearer": []}]}}, "/api/v1/tokens": {"post": {"operationId": "TokenController_create", "summary": "Create a new token (Admin only)", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTokenDto"}}}}, "responses": {"201": {"description": "<PERSON>ken created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["tokens"], "security": [{"bearer": []}]}, "get": {"operationId": "TokenController_findAll", "summary": "Get all tokens", "parameters": [{"name": "isActive", "required": true, "in": "query", "schema": {"type": "boolean"}}, {"name": "type", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return all tokens", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["tokens"]}}, "/api/v1/tokens/{id}": {"get": {"operationId": "TokenController_findOne", "summary": "Get token by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return token by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["tokens"]}, "put": {"operationId": "TokenController_update", "summary": "Update token by ID (Admin only)", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Token updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["tokens"], "security": [{"bearer": []}]}, "delete": {"operationId": "TokenController_remove", "summary": "Delete token by ID (Admin only)", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Token deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["tokens"], "security": [{"bearer": []}]}}, "/api/v1/tokens/symbol/{symbol}": {"get": {"operationId": "TokenController_findBySymbol", "summary": "Get token by symbol", "parameters": [{"name": "symbol", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return token by symbol", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["tokens"]}}, "/api/v1/tokens/balances/me": {"get": {"operationId": "TokenController_getUserBalances", "summary": "Get all token balances for the authenticated user", "parameters": [], "responses": {"200": {"description": "Return user token balances", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["tokens"], "security": [{"bearer": []}]}}, "/api/v1/tokens/balances/me/{symbol}": {"get": {"operationId": "TokenController_getUserBalanceBySymbol", "summary": "Get token balance by symbol for the authenticated user", "parameters": [{"name": "symbol", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return user token balance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["tokens"], "security": [{"bearer": []}]}}, "/api/v1/auth/wallet-login": {"post": {"operationId": "AuthController_walletLogin", "summary": "Login with blockchain wallet", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WalletLoginDto"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "400": {"description": "Invalid input data"}, "401": {"description": "Invalid signature"}}, "tags": ["auth"]}}, "/api/v1/auth/verify-token": {"post": {"operationId": "AuthController_verifyToken", "summary": "Verify JWT token", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyTokenDto"}}}}, "responses": {"200": {"description": "Token is valid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "400": {"description": "Invalid input data"}, "401": {"description": "Invalid token"}}, "tags": ["auth"]}}, "/api/v1/nfts": {"get": {"operationId": "NFTController_getAllNFTs", "summary": "Get all NFTs", "parameters": [], "responses": {"200": {"description": "Return all NFTs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["nfts"]}}, "/api/v1/nfts/me": {"get": {"operationId": "NFTController_myNFT", "summary": "Get my NFTs", "parameters": [], "responses": {"200": {"description": "Return my NFTs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "404": {"description": "NFTs not found"}}, "tags": ["nfts"], "security": [{"bearer": []}]}}, "/api/v1/nfts/listed": {"get": {"operationId": "NFTController_getListedNFTs", "summary": "Get all LISTED NFTs", "parameters": [], "responses": {"200": {"description": "Return all LISTED NFTs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "404": {"description": "NFTs not found"}}, "tags": ["nfts"], "security": [{"bearer": []}]}}, "/api/v1/nfts/sessions/{sessionId}/listed": {"get": {"operationId": "NFTController_getListedBySession", "summary": "Get all LISTED NFTs", "parameters": [{"name": "sessionId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return all LISTED NFTs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "404": {"description": "NFTs not found"}}, "tags": ["nfts"], "security": [{"bearer": []}]}}, "/api/v1/nfts/{nftId}/listing": {"post": {"operationId": "NFTController_listingNFT", "summary": "Listing NFT", "parameters": [{"name": "nftId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "NFT listed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "400": {"description": "Invalid request or insufficient balance"}, "404": {"description": "Session not found"}}, "tags": ["nfts"], "security": [{"bearer": []}]}}, "/api/v1/nfts/{nftId}/session/{sessionId}/schedule": {"post": {"operationId": "NFTController_scheduleNFT", "summary": "Schedule NFT in trading session", "parameters": [{"name": "sessionId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "nftId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "NFT scheduled successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "400": {"description": "Invalid request or insufficient balance"}, "404": {"description": "Session not found"}}, "tags": ["nfts"], "security": [{"bearer": []}]}}, "/api/v1/nfts/{nftId}/session/{sessionId}/order": {"post": {"operationId": "NFTController_orderNFT", "summary": "Buy NFT in trading session", "parameters": [{"name": "sessionId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "nftId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "NFT bought successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "400": {"description": "Invalid request or insufficient balance"}, "404": {"description": "Session not found"}}, "tags": ["nfts"], "security": [{"bearer": []}]}}, "/api/v1/nfts/orders": {"get": {"operationId": "NFTController_getMyOrders", "summary": "Get my orders", "parameters": [], "responses": {"200": {"description": "Return my orders", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}, "404": {"description": "Orders not found"}}, "tags": ["nfts"], "security": [{"bearer": []}]}}, "/api/v1/trading-sessions": {"get": {"operationId": "TradingSessionController_getSessions", "summary": "Get all trading sessions", "parameters": [], "responses": {"200": {"description": "Returns all trading sessions"}}, "tags": ["Trading Sessions"]}}, "/api/v1/trading-sessions/current": {"get": {"operationId": "TradingSessionController_getCurrentSession", "summary": "Get current trading session", "parameters": [], "responses": {"200": {"description": "Returns current trading session if exists"}}, "tags": ["Trading Sessions"]}}, "/api/v1/trading-sessions/today": {"get": {"operationId": "TradingSessionController_getTodaySessions", "summary": "Get today's trading sessions", "parameters": [], "responses": {"200": {"description": "Returns all trading sessions for today"}}, "tags": ["Trading Sessions"]}}, "/api/v1/trading-sessions/statistics": {"get": {"operationId": "TradingSessionController_getStatistics", "summary": "Get trading session statistics", "parameters": [{"name": "startDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "endDate", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns trading statistics for the specified date range"}}, "tags": ["Trading Sessions"]}}, "/api/v1/trading-sessions/{id}/schedule": {"post": {"operationId": "TradingSessionController_scheduleForSession", "summary": "Schedule for a trading session", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "Successfully scheduled for session"}}, "tags": ["Trading Sessions"], "security": [{"bearer": []}]}}, "/api/v1/trading-sessions/{id}/open": {"post": {"operationId": "TradingSessionController_openSession", "summary": "Open a trading session", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "Successfully opened session"}}, "tags": ["Trading Sessions"], "security": [{"bearer": []}]}}, "/api/v1/trading-sessions/{id}/close": {"post": {"operationId": "TradingSessionController_closeSession", "summary": "Close a trading session", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "Successfully closed session"}}, "tags": ["Trading Sessions"], "security": [{"bearer": []}]}}, "/api/v1/staking/packages": {"get": {"operationId": "StakingController_getStakingPackages", "summary": "Get available staking packages", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["staking"], "security": [{"bearer": []}]}}, "/api/v1/staking/my-stakings": {"get": {"operationId": "StakingController_getUserStakings", "summary": "Get user staking packages", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["staking"], "security": [{"bearer": []}]}}, "/api/v1/staking/stakings/{id}": {"get": {"operationId": "StakingController_getStakingDetails", "summary": "Get staking package details", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["staking"], "security": [{"bearer": []}]}}, "/api/v1/staking/create": {"post": {"operationId": "StakingController_createStaking", "summary": "Create new staking package", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStakingDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["staking"], "security": [{"bearer": []}]}}, "/api/v1/staking/api-key/admin/create": {"post": {"operationId": "StakingController_createStakingApiKey", "summary": "Deposit USDT using API Key", "parameters": [{"name": "x-wallet-address", "in": "header", "description": "Wallet Address", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStakingDto"}}}}, "responses": {"200": {"description": "Deposit USDT created successfully"}}, "tags": ["staking"]}}, "/api/v1/staking/api-key/admin/system/manual-create": {"post": {"operationId": "StakingController_createStakingApiKeySystemManual", "summary": "Deposit USDT using API Key", "parameters": [{"name": "x-wallet-address", "in": "header", "description": "Wallet Address", "schema": {"type": "string"}}, {"name": "x-api-key", "in": "header", "description": "API Key", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStakingDto"}}}}, "responses": {"200": {"description": "Deposit USDT created successfully"}}, "tags": ["staking"]}}, "/api/v1/staking/stats": {"get": {"operationId": "StakingController_getStakingStats", "summary": "Get user staking statistics", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["staking"], "security": [{"bearer": []}]}}, "/api/v1/staking/stats/summary": {"get": {"operationId": "StakingController_getStakingSummaryStats", "summary": "Get summary staking statistics", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["staking"], "security": [{"bearer": []}]}}, "/api/v1/staking/test/increment-days": {"post": {"operationId": "StakingController_incrementDays", "summary": "Increment elapsed days for staking package", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IncrementDaysDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["staking"], "security": [{"bearer": []}]}}, "/api/v1/staking/test/user-info/{id}": {"get": {"operationId": "StakingController_getUserInfo", "summary": "Get user information for testing", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/update-ranks": {"post": {"operationId": "StakingController_testUpdateRanks", "summary": "Update WManager ranks", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/calculate-daily-interest": {"post": {"operationId": "StakingController_calculateDailyInterest", "summary": "Calculate daily interest for staking packages (manual)", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"], "security": [{"bearer": []}]}}, "/api/v1/staking/test/debug-ranks": {"post": {"operationId": "StakingController_debugRanks", "summary": "Debug WManager ranks", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/force-update-rank": {"post": {"operationId": "StakingController_forceUpdateRank", "summary": "Update WManager rank for a specific user", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/setup-wm2": {"post": {"operationId": "StakingController_setupWM2", "summary": "Setup test data for WM2 rank", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/maxed-out-commission": {"post": {"operationId": "StakingController_testMaxedOutCommission", "summary": "Test rank and shareholder commission with stakingMaxedOut", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/setup-maxed-out": {"post": {"operationId": "StakingController_setupMaxedOut", "summary": "Setup test data for stakingMaxedOut", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/setup-earnings": {"post": {"operationId": "StakingController_setupEarnings", "summary": "Setup test data for total investment and total earnings", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/setup-wm3": {"post": {"operationId": "StakingController_setupWM3", "summary": "Setup test data for WM3 rank", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/setup-wm4": {"post": {"operationId": "StakingController_setupWM4", "summary": "Setup test data for WM4 rank", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/setup-wm5": {"post": {"operationId": "StakingController_setupWM5", "summary": "Setup test data for WM5 rank", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/setup-shareholder": {"post": {"operationId": "StakingController_setupShareholder", "summary": "Setup test data for shareholders", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/staking/test/test-commission": {"post": {"operationId": "StakingController_testCommission", "summary": "Test rank commission calculation", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["staking"]}}, "/api/v1/test-rank/create-data": {"post": {"operationId": "TestRankController_createTestData", "summary": "Tạo dữ liệu test", "parameters": [], "responses": {"200": {"description": "Tạo dữ liệu test thành công"}}, "tags": ["test-rank"]}}, "/api/v1/test-rank/update-ranks": {"post": {"operationId": "TestRankController_testUpdateRanks", "summary": "Test cập nh<PERSON>t cấp bậc WManager", "parameters": [], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t cấp bậc thành công"}}, "tags": ["test-rank"]}}, "/api/v1/support/tickets": {"post": {"operationId": "SupportController_createTicket", "summary": "Create a new support ticket", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTicketDto"}}}}, "responses": {"201": {"description": "Ticket created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["support"], "security": [{"bearer": []}]}, "get": {"operationId": "SupportController_getUserTickets", "summary": "Get all tickets for the current user", "parameters": [], "responses": {"200": {"description": "Tickets retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["support"], "security": [{"bearer": []}]}}, "/api/v1/support/admin/tickets": {"get": {"operationId": "SupportController_getAllTickets", "summary": "Get all tickets (admin only)", "parameters": [], "responses": {"200": {"description": "All tickets retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["support"], "security": [{"bearer": []}]}}, "/api/v1/support/tickets/{id}": {"get": {"operationId": "SupportController_getTicketById", "summary": "Get a ticket by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Ticket retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["support"], "security": [{"bearer": []}]}}, "/api/v1/support/admin/tickets/{id}": {"patch": {"operationId": "SupportController_updateTicket", "summary": "Update a ticket (admin only)", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTicketDto"}}}}, "responses": {"200": {"description": "Ticket updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseDto"}}}}}, "tags": ["support"], "security": [{"bearer": []}]}}}, "info": {"title": "WorldMall Crypto Platform API", "description": "API documentation for WorldMall Crypto Platform", "version": "1.0", "contact": {}}, "tags": [{"name": "auth", "description": "Authentication endpoints"}, {"name": "users", "description": "User management endpoints"}, {"name": "wallet", "description": "Wallet management endpoints"}, {"name": "token", "description": "Token management endpoints"}], "servers": [{"url": "http://localhost:5000", "description": "Local development"}, {"url": "https://api.worldmall.crypto", "description": "Production"}], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"ApiResponseDto": {"type": "object", "properties": {"statusCode": {"type": "number", "description": "Status code", "example": 200}, "success": {"type": "boolean", "description": "Success status", "example": true}, "message": {"type": "string", "description": "Response message", "example": "Operation successful"}, "data": {"type": "object", "description": "Response data"}, "timestamp": {"type": "string", "description": "Timestamp of the response", "example": "2025-03-05T18:30:00.000Z"}, "stack": {"type": "string", "description": "Stack trace", "example": "Error: Operation failed"}, "errorCode": {"type": "string", "description": "Error code", "example": "USER_UPDATE_PROFILE_INVALID_EMAIL"}, "errors": {"description": "Errors", "example": [{"en": "Email is required"}], "type": "array", "items": {"type": "string"}}}, "required": ["statusCode", "success", "message", "data", "timestamp", "stack", "errorCode", "errors"]}, "UpdateProfileDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address"}, "name": {"type": "string", "description": "User full name"}, "phone": {"type": "string", "description": "User phone number"}}, "required": ["email", "name", "phone"]}, "UpdateKycDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address"}, "name": {"type": "string", "description": "User full name"}, "phone": {"type": "string", "description": "User phone number"}, "nationalId": {"type": "string", "description": "User national ID number"}, "transactionHash": {"type": "string", "description": "Transaction hash"}}, "required": ["email", "name", "phone", "nationalId", "transactionHash"]}, "UpdateKycApiKeyDto": {"type": "object", "properties": {"transactionHash": {"type": "string", "description": "Transaction hash"}}, "required": ["transactionHash"]}, "TreeStats": {"type": "object", "properties": {"totalReferrals": {"type": "number"}, "totalReferralsKyc": {"type": "number"}, "totalDirectReferrals": {"type": "number"}, "totalDirectReferralsKyc": {"type": "number"}, "totalVolume": {"type": "number"}, "totalEarnings": {"type": "number"}, "totalVolumePerDay": {"type": "number"}, "totalEarningsPerDay": {"type": "number"}, "totalVolumeSession": {"type": "number"}, "totalEarningsSession": {"type": "number"}, "totalVolumeDirectReferrals": {"type": "number"}, "totalVolumePerDayDirectReferrals": {"type": "number"}, "totalEarningsDirectReferrals": {"type": "number"}, "totalEarningsPerDayDirectReferrals": {"type": "number"}, "totalVolumeSessionDirectReferrals": {"type": "number"}, "totalEarningsSessionDirectReferrals": {"type": "number"}, "totalSellVolume": {"type": "number"}, "totalSellVolumePerDay": {"type": "number"}, "totalSellVolumeSession": {"type": "number"}, "hasF1BuySession": {"type": "boolean"}, "hasF1SellSession": {"type": "boolean"}}, "required": ["totalReferrals", "totalReferralsKyc", "totalDirectReferrals", "totalDirectReferralsKyc", "totalVolume", "totalEarnings", "totalVolumePerDay", "totalEarningsPerDay", "totalVolumeSession", "totalEarningsSession", "totalVolumeDirectReferrals", "totalVolumePerDayDirectReferrals", "totalEarningsDirectReferrals", "totalEarningsPerDayDirectReferrals", "totalVolumeSessionDirectReferrals", "totalEarningsSessionDirectReferrals", "totalSellVolume", "totalSellVolumePerDay", "totalSellVolumeSession", "hasF1BuySession", "hasF1SellSession"]}, "TreeNode": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "username": {"type": "string"}, "walletAddress": {"type": "string"}, "rank": {"type": "string"}, "wManagerRank": {"type": "string"}, "totalMined": {"type": "number"}, "totalEarnings": {"type": "number"}, "totalVolume": {"type": "number"}, "totalVolumePerDay": {"type": "number"}, "totalEarningsPerDay": {"type": "number"}, "totalVolumeSession": {"type": "number"}, "totalEarningsSession": {"type": "number"}, "level": {"type": "string"}, "totalReferrals": {"type": "number"}, "totalReferralsKyc": {"type": "number"}, "directReferrals": {"type": "number"}, "directReferralsKyc": {"type": "number"}, "totalSellVolume": {"type": "number"}, "totalSellVolumePerDay": {"type": "number"}, "totalSellVolumeSession": {"type": "number"}, "hasF1BuySession": {"type": "boolean"}, "hasF1SellSession": {"type": "boolean"}, "firstDepositTime": {"type": "string", "nullable": true}, "joinedAt": {"type": "string"}, "hasLightningBolt": {"type": "boolean"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/TreeNode"}}, "hasMoreChildren": {"type": "boolean", "nullable": true}}, "required": ["id", "name", "username", "wallet<PERSON>ddress", "rank", "wManagerRank", "totalMined", "totalEarnings", "totalVolume", "totalVolumePerDay", "totalEarningsPerDay", "totalVolumeSession", "totalEarningsSession", "level", "totalReferrals", "totalReferralsKyc", "directReferrals", "directReferralsKyc", "totalSellVolume", "totalSellVolumePerDay", "totalSellVolumeSession", "hasF1BuySession", "hasF1SellSession", "firstDepositTime", "joinedAt", "hasLightningBolt", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "TreeResponseDto": {"type": "object", "properties": {"stats": {"$ref": "#/components/schemas/TreeStats"}, "tree": {"$ref": "#/components/schemas/TreeNode"}, "directReferrals": {"type": "array", "items": {"$ref": "#/components/schemas/TreeNode"}}}, "required": ["stats", "tree", "directReferrals"]}, "Wallet": {"type": "object", "properties": {"id": {"type": "string", "description": "Wallet ID"}, "userId": {"type": "string", "description": "User ID"}, "address": {"type": "string", "description": "Wallet address"}, "addressV1": {"type": "string", "description": "Wallet address", "nullable": true}, "type": {"type": "string", "description": "Wallet type (e.g., WEB3)"}, "wmBalance": {"type": "number", "description": "WM token balance"}, "wmBalanceBackup": {"type": "number", "description": "WM token balance (backup)"}, "wmLockedBalance": {"type": "number", "description": "WM token locked balance"}, "wmLockedBalanceBackup": {"type": "number", "description": "WM token locked balance (backup)"}, "usdtBalance": {"type": "number", "description": "USDT balance"}, "usdtBalanceBackup": {"type": "number", "description": "USDT balance (backup)"}, "isActive": {"type": "boolean", "description": "Is wallet active"}, "encryptedPrivateKey": {"type": "string", "description": "Encrypted private key"}, "createdAt": {"format": "date-time", "type": "string", "description": "Wallet creation timestamp"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Wallet last update timestamp"}}, "required": ["id", "userId", "address", "addressV1", "type", "wmBalance", "wmBalanceBackup", "wmLockedBalance", "wmLockedBalanceBackup", "usdtBalance", "usdtBalanceBackup", "isActive", "encryptedPrivateKey", "createdAt", "updatedAt"]}, "User": {"type": "object", "properties": {"id": {"type": "string", "description": "User ID"}, "username": {"type": "string", "description": "Username, generated from wallet address"}, "wallet": {"type": "string", "description": "Wallet address used for login"}, "email": {"type": "string", "description": "User email address"}, "name": {"type": "string", "description": "User full name"}, "phone": {"type": "string", "description": "User phone number"}, "role": {"type": "string", "description": "User role", "enum": ["ADMIN", "USER"]}, "rank": {"type": "string", "description": "User rank", "enum": ["BRONZE", "SILVER", "GOLD", "PLATINUM", "DIAMOND"]}, "wManagerRank": {"type": "string", "description": "W-Manager rank", "enum": ["WM1", "WM2", "WM3", "WM4", "WM5"]}, "isShareholder": {"type": "boolean", "description": "Whether user is shareholder"}, "miningMultiplier": {"type": "number", "description": "Mining efficiency multiplier"}, "totalMined": {"type": "number", "description": "Total mined WM tokens"}, "totalMinedBackup": {"type": "number", "description": "Total mined WM tokens (backup)"}, "lastMiningTime": {"format": "date-time", "type": "string", "description": "Last mining timestamp"}, "hasLightningBolt": {"type": "boolean", "description": "Whether user has purchased lightning bolt (permanent 2x mining)"}, "lightningBoltPrice": {"type": "number", "description": "Amount paid for lightning bolt (in USDT)"}, "lightningBoltPurchaseDate": {"format": "date-time", "type": "string", "description": "Date when lightning bolt was purchased"}, "hasMinDeposit": {"type": "boolean", "description": "Whether user has deposited minimum 5 USDT"}, "credits": {"type": "number", "description": "User credits for platform activities"}, "referralCode": {"type": "string", "description": "Unique referral code for affiliate program"}, "referredBy": {"type": "string", "description": "Referrer ID"}, "referrer": {"description": "Referrer user object", "allOf": [{"$ref": "#/components/schemas/User"}]}, "referrals": {"description": "List of referred users", "type": "array", "items": {"$ref": "#/components/schemas/User"}}, "path": {"type": "string", "description": "Tree path for efficient tree queries"}, "web3WalletId": {"type": "string", "description": "ID of user's Web3 wallet"}, "web3Wallet": {"description": "User's Web3 wallet", "allOf": [{"$ref": "#/components/schemas/Wallet"}]}, "dailyMiningRate": {"type": "number", "description": "Daily mining rate (WM per day)"}, "referralMiningBonus": {"type": "number", "description": "Bonus mining from referrals (25% of F1 daily)"}, "completedTasks": {"type": "number", "description": "Number of completed tasks"}, "totalEarnings": {"type": "number", "description": "Total earnings from referral program"}, "totalEarningsPerDay": {"type": "number", "description": "Total earnings from referral program per day"}, "totalEarningsSession": {"type": "number", "description": "Total earnings from referral program per session"}, "totalVolume": {"type": "number", "description": "Total volume from referral program"}, "totalVolumePerDay": {"type": "number", "description": "Total volume from referral program per day"}, "totalVolumeSession": {"type": "number", "description": "Total volume from referral program per session"}, "totalSellVolume": {"type": "number", "description": "Total sell volume from referral program"}, "totalSellVolumePerDay": {"type": "number", "description": "Total sell volume from referral program per day"}, "totalSellVolumeSession": {"type": "number", "description": "Total sell volume from referral program per session"}, "hasF1BuySession": {"type": "boolean", "description": "Whether user has completed F1 buy session"}, "hasF1SellSession": {"type": "boolean", "description": "Whether user has completed F1 sell session"}, "firstDepositTime": {"format": "date-time", "type": "string", "description": "First deposit timestamp"}, "lastWithdrawalTime": {"format": "date-time", "type": "string", "description": "Last withdrawal timestamp"}, "isRoot": {"type": "boolean", "description": "Whether user is root"}, "nationalId": {"type": "string", "description": "User national ID number"}, "isFirstProfileCompleted": {"type": "boolean", "description": "Whether user has completed first profile"}, "isKycCompleted": {"type": "boolean", "description": "Whether user has completed KYC"}, "isInviteCompleted": {"type": "boolean", "description": "Whether user has completed invite"}, "isNftCompleted": {"type": "boolean", "description": "Whether user has completed NFT"}, "isLocked": {"type": "boolean", "description": "Whether user is locked"}, "isPriorityBuy": {"type": "boolean", "description": "Whether user has priority to buy first in session"}, "stakingActive": {"type": "boolean", "description": "Whether user is active in staking"}, "stakingDirectActiveCount": {"type": "number", "description": "Number of direct active referrals in staking"}, "stakingRewardedMilestone": {"type": "number", "description": "Highest rewarded milestone in staking"}, "stakingTotalVolume": {"type": "number", "description": "Total staking volume"}, "stakingLeftVolume": {"type": "number", "description": "Left staking volume"}, "stakingRightVolume": {"type": "number", "description": "Right staking volume"}, "stakingTotalCommission": {"type": "number", "description": "Total staking commission"}, "stakingDirectCommission": {"type": "number", "description": "Direct staking commission"}, "stakingMatchingCommission": {"type": "number", "description": "Matching staking commission"}, "stakingRankCommission": {"type": "number", "description": "Rank staking commission"}, "stakingIsShareholder": {"type": "boolean", "description": "Whether user is shareholder in staking"}, "stakingTotalInvestment": {"type": "number", "description": "Total staking investment amount"}, "stakingTotalEarnings": {"type": "number", "description": "Total staking earnings (interest + commissions)"}, "stakingMaxedOut": {"type": "boolean", "description": "Whether user has reached max earnings (300% of investment)"}, "createdAt": {"format": "date-time", "type": "string", "description": "User creation timestamp"}, "updatedAt": {"format": "date-time", "type": "string", "description": "User last update timestamp"}}, "required": ["id", "username", "wallet", "role", "rank", "wManagerRank", "isShareholder", "miningMultiplier", "totalMined", "totalMinedBackup", "hasLightningBolt", "lightningBoltPrice", "hasMinDeposit", "credits", "referralCode", "referrals", "web3Wallet", "dailyMiningRate", "referralMiningBonus", "completedTasks", "totalEarnings", "totalEarningsPerDay", "totalEarningsSession", "totalVolume", "totalVolumePerDay", "totalVolumeSession", "totalSellVolume", "totalSellVolumePerDay", "totalSellVolumeSession", "hasF1BuySession", "hasF1SellSession", "stakingDirectActiveCount", "stakingRewardedMilestone", "stakingTotalVolume", "stakingLeftVolume", "stakingRightVolume", "stakingTotalCommission", "stakingDirectCommission", "stakingMatchingCommission", "stakingRankCommission", "stakingIsShareholder", "stakingTotalInvestment", "stakingTotalEarnings", "stakingMaxedOut", "createdAt", "updatedAt"]}, "UpdateUserBalanceDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Token type to update", "enum": ["USDT", "WM"], "example": "USDT"}, "amount": {"type": "number", "description": "Amount to update", "example": 100.5, "minimum": 0}, "operation": {"type": "string", "description": "Operation type", "enum": ["add", "subtract", "set"], "example": "add"}, "isCreateTransaction": {"type": "boolean", "description": "Whether to create transaction history record", "example": true, "default": true}}, "required": ["type", "amount", "operation"]}, "UpdateWalletDto": {"type": "object", "properties": {"wmBalance": {"type": "number", "description": "WM token balance"}, "usdtBalance": {"type": "number", "description": "USDT balance"}, "isActive": {"type": "boolean", "description": "Is wallet active"}, "metadata": {"type": "object", "description": "Additional metadata"}}, "required": ["wmBalance", "usdtBalance", "isActive", "metadata"]}, "DepositUsdtDto": {"type": "object", "properties": {"amount": {"type": "number", "description": "Amount USDT to deposit"}, "transactionHash": {"type": "string", "description": "Transaction hash"}}, "required": ["amount", "transactionHash"]}, "WithdrawUsdtDto": {"type": "object", "properties": {"amount": {"type": "number", "description": "Amount USDT to withdraw"}}, "required": ["amount"]}, "TransferWmDto": {"type": "object", "properties": {"amount": {"type": "number", "description": "Amount WM to transfer"}, "referralCode": {"type": "string", "description": "Wallet address to transfer"}, "walletType": {"type": "string", "description": "Wallet type to transfer"}}, "required": ["amount", "referralCode", "walletType"]}, "TransferUsdtDto": {"type": "object", "properties": {"amount": {"type": "number", "description": "Amount USDT to transfer", "example": 100.5, "minimum": 0.01}, "recipient": {"type": "string", "description": "Recipient wallet address or referral code", "example": "****************************************** or ABC123"}, "note": {"type": "string", "description": "Transfer note/memo", "example": "Payment for services"}}, "required": ["amount", "recipient"]}, "AdjustWmDto": {"type": "object", "properties": {"amount": {"type": "number", "description": "Amount WM to adjust"}, "type": {"type": "string", "description": "Type to adjust"}, "note": {"type": "string", "description": "Type to adjust"}}, "required": ["amount", "type", "note"]}, "CreateTokenDto": {"type": "object", "properties": {"symbol": {"type": "string", "description": "Token symbol (e.g., WM, USDT)"}, "name": {"type": "string", "description": "Token name (e.g., WorldMall Token, Tether USD)"}, "type": {"type": "string", "description": "Token type", "enum": ["NATIVE", "STABLECOIN", "UTILITY", "GOVERNANCE", "REWARD"]}, "totalSupply": {"type": "number", "description": "Total supply of the token"}, "circulatingSupply": {"type": "number", "description": "Circulating supply of the token"}, "contractAddress": {"type": "string", "description": "Token contract address (for blockchain tokens)"}, "decimals": {"type": "number", "description": "Token decimals"}, "isActive": {"type": "boolean", "description": "Whether the token is active"}, "logoUrl": {"type": "string", "description": "Token logo URL"}, "description": {"type": "string", "description": "Token description"}}, "required": ["symbol", "name", "type"]}, "WalletLoginDto": {"type": "object", "properties": {"wallet": {"type": "string", "description": "Wallet address"}, "signature": {"type": "string", "description": "Signature from wallet"}, "message": {"type": "string", "description": "The message that was signed"}, "ref": {"type": "string", "description": "Referral code (optional)"}}, "required": ["wallet", "signature", "message"]}, "VerifyTokenDto": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT token to verify", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}, "required": ["token"]}, "CreateStakingDto": {"type": "object", "properties": {"packageCode": {"type": "string", "description": "Package code", "example": "PHOENIX", "enum": ["PHOENIX", "SPIRIT_TURTLE", "UNICORN", "DRAGON", "DRAGON_LORD", "ETERNAL_DRAGON", "SOVEREIGN_DRAGON"]}, "durationDays": {"type": "number", "description": "Duration in days", "example": 30, "enum": [30, 60, 90, 180, 360]}}, "required": ["packageCode", "durationDays"]}, "IncrementDaysDto": {"type": "object", "properties": {"stakingId": {"type": "string", "description": "Staking transaction ID (optional, if not provided will increment all active stakings)"}, "days": {"type": "number", "description": "Number of days to increment (default: 1)", "default": 1}}}, "CreateTicketDto": {"type": "object", "properties": {"content": {"type": "string", "description": "Ticket content"}, "txHash": {"type": "string", "description": "Transaction hash (if applicable)"}, "walletAddress": {"type": "string", "description": "BNB wallet address"}}, "required": ["content", "wallet<PERSON>ddress"]}, "UpdateTicketDto": {"type": "object", "properties": {"status": {"type": "string", "description": "Ticket status", "enum": ["pending", "processing", "resolved"]}, "response": {"type": "string", "description": "Admin response to the ticket"}}}}}}