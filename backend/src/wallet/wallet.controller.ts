import { BadRequestException, Body, Controller, Delete, Get, Logger, NotFoundException, Param, Patch, Post, Query, Request, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiHeader, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ApiKeyGuard } from '../auth/guards/api-key.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiResponseDto } from '../common/dto/api-response.dto';
import { TransactionStatus, TransactionType } from '../common/enums/transaction.enum';
import { SystemConfigService } from '../common/services/system-config.service';
import { AdjustWmDto } from './dto/adjust-wm-wallet.dto';
import { DepositUsdtDto } from './dto/deposit-usdt-wallet.dto';
import { TransferWmDto } from './dto/transfer-wm-wallet.dto';
import { TransferUsdtDto } from './dto/transfer-usdt.dto';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { WithdrawUsdtDto } from './dto/withdraw-usdt-wallet.dto';
import { TransactionExpiryService } from './transaction-expiry.service';
import { WalletService } from './wallet.service';

@ApiTags('wallet')
@Controller('wallet')
export class WalletController {
  private readonly logger = new Logger(WalletController.name);

  constructor(
    public readonly walletService: WalletService,
    private readonly systemConfigService: SystemConfigService,
    private readonly transactionExpiryService: TransactionExpiryService
  ) {}

  @Get('default')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get default wallet information' })
  @ApiResponse({ status: 200, description: 'Return default wallet info', type: ApiResponseDto })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  async getDefaultWallet(@Request() req): Promise<ApiResponseDto<string>> {
      const defaultWallet = await this.walletService.getDefaultWallet(req.user.id);
      return ApiResponseDto.success(defaultWallet, 'Default wallet retrieved successfully');
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user wallet information' })
  @ApiResponse({ status: 200, description: 'Return current user wallet info', type: ApiResponseDto })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  async getCurrentUserWallet(@Request() req) {
    return this.walletService.findByUserId(req.user.id, 'WEB3');
  }

  @Get('api-key/me')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiHeader({ name: 'x-wallet-address', description: 'Wallet Address' })
  @ApiOperation({ summary: 'Get current user wallet information' })
  @ApiResponse({ status: 200, description: 'Return current user wallet info' })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  async getCurrentUserWalletApiKey(@Request() req) {
    const walletAddress = req.headers['x-wallet-address'];
    if (!walletAddress) {
      throw new BadRequestException('Wallet address is required in x-wallet-address header');
    }

    const wallet = await this.walletService.findByAddress(walletAddress);
    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    return wallet;
  }

  @Get()
  @ApiOperation({ summary: 'Get all wallets' })
  @ApiResponse({ status: 200, description: 'Return all wallets' })
  async findAll(@Query('isActive') isActive?: boolean) {
    return this.walletService.findAll({ isActive });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get wallet by ID' })
  @ApiResponse({ status: 200, description: 'Return wallet by ID' })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  async findOne(@Param('id') id: string) {
    return this.walletService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update wallet by ID' })
  @ApiResponse({ status: 200, description: 'Wallet updated successfully' })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  async update(@Param('id') id: string, @Body() updateWalletDto: UpdateWalletDto) {
    return this.walletService.update(id, updateWalletDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete wallet by ID' })
  @ApiResponse({ status: 200, description: 'Wallet deleted successfully' })
  async remove(@Param('id') id: string) {
    return this.walletService.remove(id);
  }

  @Get(':walletId/transactions')
  @ApiOperation({ summary: 'Get transactions by wallet ID' })
  @ApiResponse({ status: 200, description: 'Return transactions by wallet ID' })
  async getTransactions(
    @Param('walletId') walletId: string,
    @Query('status') status?: TransactionStatus,
    @Query('type') type?: TransactionType
  ) {
    return this.walletService.getTransactions(walletId, { status, type });
  }

  /**
   * Nạp USDT
   */
  @Post('deposit/usdt')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Deposit USDT' })
  @ApiResponse({ status: 200, description: 'Deposit USDT created successfully' })
  async depositUsdt(@Request() req, @Body() depositUsdtDto: DepositUsdtDto) {
    const userId = req.user.id;
    return await this.walletService.depositUsdt(userId, depositUsdtDto);
  }

  @Post('api-key/deposit/usdt')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiHeader({ name: 'x-wallet-address', description: 'Wallet Address' })
  @ApiOperation({ summary: 'Deposit USDT using API Key' })
  @ApiResponse({ status: 200, description: 'Deposit USDT created successfully' })
  async depositUsdtApiKey(@Request() req, @Body() depositUsdtDto: DepositUsdtDto) {
    const walletAddress = req.headers['x-wallet-address'];
    if (!walletAddress) {
      throw new BadRequestException('Wallet address is required in x-wallet-address header');
    }

    const wallet = await this.walletService.findByAddress(walletAddress);
    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    return await this.walletService.depositUsdt(wallet.userId, depositUsdtDto);
  }

  /**
   * Nạp WM
   */
  @Post('deposit/wm')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Deposit WM' })
  @ApiResponse({ status: 200, description: 'Deposit WM created successfully' })
  async depositWm(@Request() req, @Body() depositWmDto: DepositUsdtDto) {
    const userId = req.user.id;
    return await this.walletService.depositWm(userId, depositWmDto);
  }

  @Get('transactions/history')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get transaction history for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Return transaction history' })
  async getTransactionHistory(
    @Request() req,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('type') type?: TransactionType | 'ALL'
  ) {
    const userId = req.user.id;
    const result = await this.walletService.getTransactionHistory(userId, page, limit, type);
    return ApiResponseDto.success(result, 'Transaction history retrieved successfully');
  }

  @Get('api-key/transactions/history')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiOperation({ summary: 'Get transaction history for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Return transaction history' })
  async getTransactionHistoryApiKey(
      @Request() req,
      @Query('page') page: number = 1,
      @Query('limit') limit: number = 10,
      @Query('type') type?: TransactionType | 'ALL'
  ) {
        const walletAddress=req.headers['x-wallet-address'];
      if(!walletAddress){
        throw new BadRequestException('Wallet address is required in x-wallet-address header');
      }
      const wallet=await this.walletService.findByAddress(walletAddress);
      if(!wallet){
        throw new NotFoundException('Wallet not found');
      }
      const result=await this.walletService.getTransactionHistory(wallet.userId,page,limit, type);
      return ApiResponseDto.success(result,'Transaction history retrieved successfully');
  }

  @Post('task/complete')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Complete a task and receive reward' })
  @ApiResponse({ status: 200, description: 'Task completed successfully' })
  async completeTask(
    @Request() req,
    @Body() taskDto: { taskId: string; rewardAmount: number }
  ) {
    const userId = req.user.id;
    const result = await this.walletService.completeTask(
      userId,
      taskDto.taskId,
      taskDto.rewardAmount
    );
    return ApiResponseDto.success(
      result,
      `Task completed successfully. Received ${taskDto.rewardAmount} WM tokens.`
    );
  }

  /**
   * Mua tia sét
   */
  @Post('lightning')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new Lightning Bolt purchase' })
  @ApiResponse({ status: 201, description: 'Lightning Bolt purchase created successfully' })
  async purchaseLightning(@Request() req, @Body() body: { transactionHash: string }) {
    const userId = req.user.id;
    return await this.walletService.purchaseLightning(userId, body.transactionHash);
  }

  /**
   * Mua vị trí (ưu tiên)
   */
  @Post('priority')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new priority purchase' })
  @ApiResponse({ status: 201, description: 'Priority purchase created successfully' })
  async purchasePriority(@Request() req, @Body() body: { transactionHash: string }) {
    const userId = req.user.id;
    return await this.walletService.purchasePriority(userId, body.transactionHash);
  }

  /**
   * Khai thác WM
   */
  @Post('mining')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mine WM tokens' })
  @ApiResponse({ status: 200, description: 'Mining successful', type: ApiResponseDto })
  @ApiResponse({ status: 400, description: 'Already mined today' })
  async mineWm(@Request() req){
      const userId = req.user.id;
      return await this.walletService.mineWm(userId);
  }

  /**
   * Phần rút tiền, người dùng gửi một lệnh rút với USDT
   * - Sau khi nhận được lưu vào DB với trạng thái PENDING
   * - Sau khi admin duyệt (gửi qua bot telegram để duyệt), cập nhật lại trạng thái thành SUCCESS và cập nhật lại số dư vào DB
   * - Nếu admin từ chối, cập nhật lại trạng thái thành FAILED
   */
  @Post('withdraw/usdt')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Withdraw USDT' })
  @ApiResponse({ status: 200, description: 'Withdrawal successful' })
  async withdrawUsdt(@Request() req, @Body() withdrawUsdtDto: WithdrawUsdtDto) {
    const userId = req.user.id;
    return await this.walletService.withdrawUsdt(userId, withdrawUsdtDto);
  }

  @Post('api-key/withdraw/usdt')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiHeader({ name: 'x-wallet-address', description: 'Wallet Address' })
  @ApiOperation({ summary: 'Withdraw USDT using API Key' })
  @ApiResponse({ status: 200, description: 'Withdrawal successful' })
  async withdrawUsdtApiKey(@Request() req, @Body() withdrawUsdtDto: WithdrawUsdtDto) {
    const walletAddress = req.headers['x-wallet-address'];
    if (!walletAddress) {
      throw new BadRequestException('Wallet address is required in x-wallet-address header');
    }

    const wallet = await this.walletService.findByAddress(walletAddress);
    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    return await this.walletService.withdrawUsdt(wallet.userId, withdrawUsdtDto);
  }

  @Post('transfer/wm')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Transfer WM' })
  @ApiResponse({ status: 200, description: 'Transfer successful' })
  async transferWm(@Request() req, @Body() transferWmDto: TransferWmDto) {
    const userId = req.user.id;
    return await this.walletService.transferWm(userId, transferWmDto);
  }

  @Post('api-key/transfer/wm')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiHeader({ name: 'x-wallet-address', description: 'Wallet Address' })
  @ApiOperation({ summary: 'Transfer WM using API Key' })
  @ApiResponse({ status: 200, description: 'Transfer successful' })
  async transferWmApiKey(@Request() req, @Body() transferWmDto: TransferWmDto) {
    const walletAddress = req.headers['x-wallet-address'];
    if (!walletAddress) {
      throw new BadRequestException('Wallet address is required in x-wallet-address header');
    }

    const wallet = await this.walletService.findByAddress(walletAddress);
    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    return await this.walletService.transferWm(wallet.userId, transferWmDto);
  }

  @Post('transfer/usdt')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Transfer USDT to another wallet' })
  @ApiResponse({ status: 200, description: 'Transfer successful' })
  async transferUsdt(@Request() req, @Body() transferUsdtDto: TransferUsdtDto) {
    const userId = req.user.id;
    return await this.walletService.transferUsdt(userId, transferUsdtDto);
  }

  @Post('api-key/transfer/usdt')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiHeader({ name: 'x-wallet-address', description: 'Wallet Address' })
  @ApiOperation({ summary: 'Transfer USDT using API Key' })
  @ApiResponse({ status: 200, description: 'Transfer successful' })
  async transferUsdtApiKey(@Request() req, @Body() transferUsdtDto: TransferUsdtDto) {
    const walletAddress = req.headers['x-wallet-address'];
    if (!walletAddress) {
      throw new BadRequestException('Wallet address is required in x-wallet-address header');
    }

    const wallet = await this.walletService.findByAddress(walletAddress);
    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    return await this.walletService.transferUsdt(wallet.userId, transferUsdtDto);
  }

  @Post('api-key/adjust/wm')
  @UseGuards(ApiKeyGuard)
  @ApiHeader({ name: 'x-api-key', description: 'API Key' })
  @ApiHeader({ name: 'x-wallet-address', description: 'Wallet Address' })
  @ApiOperation({ summary: 'Adjust WM using API Key' })
  @ApiResponse({ status: 200, description: 'Adjust successful' })
  async adjustWmApiKey(@Request() req, @Body() adjustWmDto: AdjustWmDto) {
    const walletAddress = req.headers['x-wallet-address'];
    if (!walletAddress) {
      throw new BadRequestException('Wallet address is required in x-wallet-address header');
    }

    const wallet = await this.walletService.findByAddress(walletAddress);
    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    return await this.walletService.adjustWm(wallet.userId, adjustWmDto);
  }

  /**
   * Kiểm tra và tự động hủy các giao dịch rút tiền quá hạn
   */
  @Post('auto-cancel-pending-withdrawals')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Auto-cancel pending withdrawal transactions older than specified hours' })
  @ApiResponse({ status: 200, description: 'Pending withdrawals auto-cancelled successfully' })
  async autoCancelPendingWithdrawals(
    @Query('hours') hours: number = 24
  ) {
    this.logger.log(`Manually triggering auto-cancellation for pending withdrawals older than ${hours} hours`);
    await this.transactionExpiryService.checkAndCancelPendingWithdrawals(hours);
    return ApiResponseDto.success(
      { processed: true },
      'Pending withdrawals auto-cancelled successfully'
    );
  }
}
