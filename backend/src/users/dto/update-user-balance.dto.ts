import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsEnum, IsBoolean, IsOptional, Min } from 'class-validator';

export class UpdateUserBalanceDto {
  @ApiProperty({ 
    description: 'Token type to update',
    enum: ['USDT', 'WM'],
    example: 'USDT'
  })
  @IsString()
  @IsEnum(['USDT', 'WM'], { message: 'Type must be USDT or WM' })
  type: 'USDT' | 'WM';

  @ApiProperty({ 
    description: 'Amount to update',
    example: 100.50,
    minimum: 0
  })
  @IsNumber()
  @Min(0, { message: 'Amount must be a positive number' })
  amount: number;

  @ApiProperty({ 
    description: 'Operation type',
    enum: ['add', 'subtract', 'set'],
    example: 'add'
  })
  @IsString()
  @IsEnum(['add', 'subtract', 'set'], { message: 'Operation must be add, subtract, or set' })
  operation: 'add' | 'subtract' | 'set';

  @ApiProperty({ 
    description: 'Whether to create transaction history record',
    example: true,
    required: false,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  isCreateTransaction?: boolean = true;
}
