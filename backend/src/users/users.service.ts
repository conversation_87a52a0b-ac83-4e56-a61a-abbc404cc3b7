import { BadRequestException, ConflictException, forwardRef, Inject, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import moment from 'moment-timezone';
import { TransactionStatus, TransactionType } from 'src/common/enums/transaction.enum';
import { generateUsernameFromWallet, isValidEthereumAddress } from 'src/common/utils';
import { TokenBalance } from 'src/token/entities/token-balance.entity';
import { Token } from 'src/token/entities/token.entity';
import { TokenService } from 'src/token/token.service';
import { Transaction } from 'src/wallet/entities/transaction.entity';
import { Wallet } from 'src/wallet/entities/wallet.entity';
import { Brackets, DataSource, QueryRunner, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { UserRank, UserRole } from '../common/enums/user.enum';
import { getCurrentTime } from "../common/utils/date.utils";
import { WalletService } from '../wallet/wallet.service';
import { CreateUserDto } from './dto/create-user.dto';
import { FinancialReportDto } from './dto/financial-report.dto';
import { TreeNode } from './dto/tree-response.dto';
import { UpdateKycDto } from './dto/update-kyc.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @Inject(forwardRef(() => WalletService))
    private readonly walletService: WalletService,
    @InjectDataSource() private dataSource: DataSource,
    @Inject(forwardRef(() => TokenService))
    private readonly tokenService: TokenService,
  ) { }

  async create(createUserDto: CreateUserDto): Promise<User> {
    // Kiểm tra email đã tồn tại
    const existingEmail = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });
    if (existingEmail) {
      throw new ConflictException('Email already exists');
    }

    // Kiểm tra username đã tồn tại
    const existingUsername = await this.userRepository.findOne({
      where: { username: createUserDto.username },
    });
    if (existingUsername) {
      throw new ConflictException('Username already exists');
    }

    // Tạo mã giới thiệu ngẫu nhiên
    const referralCode = uuidv4().substring(0, 8).toUpperCase();

    // Tạo user mới
    const user = this.userRepository.create({
      ...createUserDto,
      referralCode,
      rank: UserRank.BRONZE,
      miningMultiplier: 1,
    });

    if (createUserDto.referrerCode) {
      const referrer = await this.userRepository.findOne({
        where: { referralCode: createUserDto.referrerCode },
      });
      if (referrer) {
        user.referredBy = referrer.id;
        user.path = referrer.path ? `${referrer.path}.${referrer.id}` : referrer.id;
      }
    } else {
      user.path = '';
    }

    return this.userRepository.save(user);
  }

  async count(): Promise<number> {
    return this.userRepository.count();
  }

  async findAll(): Promise<User[]> {
    return this.userRepository.find();
  }

  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['referrer', 'referrals', 'web3Wallet'],
    });

    if (!user) {
      throw new NotFoundException({
        message: { en: 'User not found' },
        code: 'USER_NOT_FOUND',
      });
    }

    return user;
  }

  async findByEmail(email: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException({
        message: { en: 'User not found' },
        code: 'USER_NOT_FOUND',
      });
    }

    return user;
  }

  async findByUsername(username: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { username },
    });

    if (!user) {
      throw new NotFoundException(`User with username ${username} not found`);
    }

    return user;
  }

  async findByReferralCode(referralCode: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { referralCode },
    });

    return user;
  }

  async getUserByWalletAddress(walletAddress: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { wallet: walletAddress },
    });

    return user;
  }

  async findByWalletAddress(walletAddress: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { wallet: walletAddress },
    });

    if (!user) {
      throw new NotFoundException(`User with wallet address ${walletAddress} not found`);
    }

    return user;
  }

  async countF1Kyc(userId: string): Promise<number> {
    const f1KycCount = await this.userRepository.count({
      where: {
        isKycCompleted: true,
        referredBy: userId,
      },
    });
    return f1KycCount;
  }

  async updateProfile(id: string, updateProfileDto: UpdateProfileDto): Promise<User> {
    this.logger.log(`[updateProfile] Cập nhật thông tin cho người dùng ${id}`);

    const user = await this.findOne(id);

    // Kiểm tra xem user đã cập nhật hồ sơ hay chưa
    const isFirstProfileCompleted = user.isFirstProfileCompleted;

    // Chỉ truy vấn token nếu cần thiết
    let token = null;
    if (!isFirstProfileCompleted) {
      token = await this.tokenService.findBySymbol('WM');
      if (!token) {
        this.logger.error(`[updateProfile] Token not found`);
        throw new InternalServerErrorException({
          message: {
            en: 'Token not found',
          },
          code: 'TOKEN_NOT_FOUND'
        });
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Cập nhật thông tin profile
      user.email = updateProfileDto.email;
      user.name = updateProfileDto.name;
      user.phone = updateProfileDto.phone;
      if (!isFirstProfileCompleted) user.isFirstProfileCompleted = true;

      const savedUser = await queryRunner.manager.save(User, user);

      // Xử lý phần thưởng cho lần cập nhật đầu tiên
      if (!isFirstProfileCompleted) {
        // Sử dụng một giá trị thời gian nhất quán
        const now = getCurrentTime();

        const userWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: savedUser.id } });

        // Tạo giao dịch thưởng +1WM
        const transaction = queryRunner.manager.create(Transaction, {
          walletId: userWallet.id,
          userId: savedUser.id,
          tokenId: token.id,
          type: TransactionType.UPDATE_PROFILE_BONUS,
          amount: 1,
          status: TransactionStatus.COMPLETED,
          reference: savedUser.id,
          note: `Bonus for first profile update (+1 WM)`,
          transactionAt: now,
          createdAt: now,
          updatedAt: now,
        });

        const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

        // Cập nhật thưởng +1WM vào tài khoản
        await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, userWallet, token, 'wmBalance', savedTransaction, 1, 'add', now);
      }

      await queryRunner.commitTransaction();

      return savedUser;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      // Xử lý lỗi chi tiết hơn
      this.logger.error(`[updateProfile] Lỗi khi cập nhật thông tin: ${error.message}`, error.stack);
      if (error instanceof BadRequestException ||
          error instanceof ConflictException ||
          error instanceof InternalServerErrorException) {
        throw error; // Giữ nguyên lỗi đã xác định
      }

      throw new BadRequestException({
        message: {
          en: `Failed to update profile. Please try again later.`,
        },
        code: 'USER_UPDATE_PROFILE_FAILED'
      });
    } finally {
      await queryRunner.release();
    }
  }

  async updateKycByWalletAddress(walletAddress: string, updateKycDto: UpdateKycDto): Promise<User> {
    const user = await this.findByWalletAddress(walletAddress);
    return this.updateKyc(user.id, updateKycDto);
  }

  async updateKyc(id: string, updateKycDto: UpdateKycDto): Promise<User> {
    const user = await this.findOne(id);

    // Kiểm tra xem người dùng đã hoàn thành KYC chưa
    if (user.isKycCompleted) {
      this.logger.warn(`[updateKyc] Người dùng ${id} đã hoàn thành KYC trước đó`);
      throw new BadRequestException({
        message: {
          en: 'KYC already completed',
        },
        code: 'USER_UPDATE_KYC_ALREADY_COMPLETED'
      });
    }

    // Kiểm tra transaction hash nếu cần
    if (!updateKycDto.transactionHash) {
      this.logger.error(`[updateKyc] Thiếu transaction hash cho người dùng ${id}`);
      throw new BadRequestException({
        message: {
          en: 'Transaction hash is required',
        },
        code: 'USER_UPDATE_KYC_MISSING_TRANSACTION_HASH'
      });
    }

    // Kiểm tra trạng thái giao dịch blockchain
    const transactionStatus = await this.walletService.checkTransactionStatus(updateKycDto.transactionHash);
    if (!transactionStatus || transactionStatus.status !== '1' || (transactionStatus.result && transactionStatus.result.status !== '1')) {
      this.logger.warn(`[updateKyc] Giao dịch ${updateKycDto.transactionHash} không được xác nhận`);
      throw new BadRequestException({
        message: {
          en: 'Transaction is not confirmed or invalid response',
        },
        code: 'TRANSACTION_NOT_CONFIRMED',
      });
    }

    // Lấy thông tin token
    const [wmToken, usdtToken] = await Promise.all([
      this.tokenService.findBySymbol('WM'),
      this.tokenService.findBySymbol('USDT')
    ]);

    // Kiểm tra token tồn tại
    if (!wmToken || !usdtToken) {
      this.logger.error(`[updateKyc] Không tìm thấy token WM hoặc USDT`);
      throw new InternalServerErrorException({
        message: {
          en: 'Required tokens not found in the system',
        },
        code: 'TOKEN_NOT_FOUND'
      });
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Cập nhật thông tin profile
      user.email = updateKycDto.email;
      user.name = updateKycDto.name;
      user.phone = updateKycDto.phone;
      user.nationalId = updateKycDto.nationalId;
      user.isKycCompleted = true;

      const savedUser = await queryRunner.manager.save(User, user);

      const userWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: savedUser.id } });

      // Sử dụng một giá trị thời gian nhất quán
      const now = getCurrentTime();

      // Tạo giao dịch phí KYC
      await queryRunner.manager.save(Transaction, {
        walletId: userWallet.id,
        userId: savedUser.id,
        tokenId: usdtToken.id,
        type: TransactionType.KYC_PURCHASE,
        amount: -10,
        status: TransactionStatus.COMPLETED,
        reference: savedUser.id,
        note: `KYC verification fee (-10USDT)`,
        txHash: updateKycDto.transactionHash,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      // Tạo giao dịch thưởng +5WM
      const transaction = queryRunner.manager.create(Transaction, {
        walletId: userWallet.id,
        userId: savedUser.id,
        tokenId: wmToken.id,
        type: TransactionType.KYC_VERIFY_BONUS,
        amount: 5,
        status: TransactionStatus.COMPLETED,
        reference: savedUser.id,
        note: `Bonus for completing KYC verification (+5 WM)`,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

      // Cập nhật airdrop +5WM
      await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, userWallet, wmToken, 'wmBalance', savedTransaction, 5, 'add', now);

      // Cập nhật hoa hồng hệ thống 0.5 USDT/đời, hoa hồng 10 đời
      await this.updateSystemCommission(queryRunner, savedUser, usdtToken);

      await queryRunner.commitTransaction();
      return savedUser;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      // Xử lý lỗi chi tiết hơn
      this.logger.error(`[updateKyc] Lỗi khi cập nhật KYC: ${error.message}`, error.stack);

      if (error instanceof BadRequestException ||
          error instanceof ConflictException ||
          error instanceof InternalServerErrorException) {
        throw error; // Giữ nguyên lỗi đã xác định
      }

      throw new BadRequestException({
        message: {
          en: `Failed to update KYC. Please try again later.`,
        },
        code: 'USER_UPDATE_KYC_FAILED'
      });
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Cập nhật hoa hồng hệ thống
   * Thuật toán:
   * - Dùng 5 USDT để tính toán hoa hồng hệ thống
   * - Dựa vào user.path để tính toán hoa hồng hệ thống
   * - Phân bổ đều cho 10 cha gần nhất mỗi người 0.5 USDT (Nếu ít hơn 10 cha thì phân bổ theo số lượng cha)
   */
  async updateSystemCommission(queryRunner: QueryRunner, user: User, usdtToken: Token, context: 'kyc' | 'lightning' = 'kyc'): Promise<void> {
    this.logger.log(`[UsersService] Cập nhật hoa hồng hệ thống cho ${user.id}`);

    // Kiểm tra path của user
    if (!user.path) {
      this.logger.warn(`[UsersService] Người dùng ${user.id} không có path, bỏ qua phân bổ hoa hồng`);
      return;
    }

    // Lấy danh sách ID của các cha từ path
    let parentIds: string[] = [];

    if (user.path) {
      parentIds = user.path
        .split('.')
        .filter(id => !!id)
        .reverse()
        .slice(0, 10);
    }

    // Đoạn code kiểm tra parentIds.length === 0 vẫn hoạt động đúng sau khi sửa lỗi
    if (parentIds.length === 0) {
      this.logger.warn(`[UsersService] Người dùng ${user.id} không có cha hoặc path không hợp lệ, bỏ qua phân bổ hoa hồng`);
      return;
    }

    // Tính toán số tiền hoa hồng cho mỗi cấp
    const commissionPerLevel = 0.5; // +0.5 USDT mỗi cấp

    // Phân bổ hoa hồng cho từng cấp cha
    for (const parentId of parentIds) {
      const parent = await queryRunner.manager.findOne(User, { where: { id: parentId } });
      if (!parent) {
        this.logger.warn(`[UsersService] Cha ${parentId} không tồn tại, bỏ qua phân bổ hoa hồng`);
        continue;
      }

      // Nếu chưa kyc thì không cập nhật hoa hồng
      if (!parent.isKycCompleted) {
        this.logger.warn(`[UsersService] Parent ${parentId} has not completed KYC, skipping commission`);
        continue;
      }

      const parentWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: parent.id } });

      const now = moment().toDate();

      // Cập nhật giao dịch (hoa hồng hệ thống)
      const transaction = queryRunner.manager.create(Transaction, {
        walletId: parentWallet.id,
        userId: parent.id,
        tokenId: usdtToken.id,
        type: context === 'lightning' ? TransactionType.SYSTEM_COMMISSION : TransactionType.KYC_VERIFY_COMMISSION,
        amount: commissionPerLevel,
        status: TransactionStatus.COMPLETED,
        reference: user.id,
        note: context === 'lightning' ? `Purchased Lightning Bolt commission for ${user.wallet} (+${commissionPerLevel}USDT)` : `KYC verify commission for ${user.wallet} (+${commissionPerLevel}USDT)`,
        transactionAt: now,
        createdAt: now,
        updatedAt: now,
      });

      const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

      // Cập nhật số dư +0.5USDT
      await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, parentWallet, usdtToken, 'usdtBalance', savedTransaction, commissionPerLevel, 'add', now);

      // Cập nhật thống kê hoa hồng của parent
      parent.totalEarnings = Number(parent.totalEarnings || 0) + commissionPerLevel;

      await queryRunner.manager.save(parent);
    }
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    // Convert decimal fields to numbers before updating
    if (updateUserDto.referralMiningBonus !== undefined) {
      updateUserDto.referralMiningBonus = Number(updateUserDto.referralMiningBonus);
    }
    if (updateUserDto.totalEarnings !== undefined) {
      updateUserDto.totalEarnings = Number(updateUserDto.totalEarnings);
    }
    if (updateUserDto.totalMined !== undefined) {
      updateUserDto.totalMined = Number(updateUserDto.totalMined);
    }

    Object.assign(user, updateUserDto);
    return this.userRepository.save(user);
  }

  /**
   * Update user role (admin only)
   * @param id User ID
   * @param role New role
   * @returns Updated user
   */
  async updateUserRole(id: string, role: UserRole): Promise<User> {
    const user = await this.findOne(id);
    user.role = role;
    return this.userRepository.save(user);
  }

  async updateRank(id: string, totalMined: number): Promise<User> {
    const user = await this.findOne(id);

    // Cập nhật rank dựa trên số lượng WM đã đào được
    if (totalMined >= 10000) {
      user.rank = UserRank.DIAMOND;
    } else if (totalMined >= 1000) {
      user.rank = UserRank.PLATINUM;
    } else if (totalMined >= 100) {
      user.rank = UserRank.GOLD;
    } else if (totalMined >= 50) {
      user.rank = UserRank.SILVER;
    }

    return this.userRepository.save(user);
  }

  async updateMiningMultiplier(id: string, multiplier: number): Promise<User> {
    const user = await this.findOne(id);
    user.miningMultiplier = multiplier;
    return this.userRepository.save(user);
  }

  async remove(id: string): Promise<void> {
    await this.userRepository.delete(id);
  }

  async createWithWallet(walletAddress: string, referrer: User): Promise<User> {
    // Kiểm tra định dạng địa chỉ ví
    if (!isValidEthereumAddress(walletAddress)) {
      this.logger.error(`[UsersService] Địa chỉ ví không hợp lệ: ${walletAddress}`);
      throw new BadRequestException('Invalid wallet address format');
    }

    // Kiểm tra xem địa chỉ ví đã tồn tại chưa
    const existingWallet = await this.userRepository.findOne({ where: { wallet: walletAddress } });
    if (existingWallet) {
      this.logger.error(`[UsersService] Địa chỉ ví đã tồn tại: ${walletAddress}`);
      throw new ConflictException('Wallet address already exists');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Lấy thông tin token một lần và sử dụng lại
      const [usdtToken, wmToken] = await Promise.all([
        this.tokenService.findBySymbol('USDT'),
        this.tokenService.findBySymbol('WM')
      ]);

      if (!usdtToken || !wmToken) {
        throw new InternalServerErrorException('Required tokens not found in the system');
      }

      // Tạo username từ địa chỉ ví và đảm bảo duy nhất
      let username = generateUsernameFromWallet(walletAddress);
      const existingUsername = await this.userRepository.findOne({ where: { username } });
      if (existingUsername) {
        // Nếu username đã tồn tại, thêm 4 ký tự ngẫu nhiên
        username = `${username}${uuidv4().substring(0, 4)}`;
      }

      const referralCode = uuidv4().substring(0, 8).toUpperCase();

      // 1. Tạo mới người dùng
      const user = new User();
      user.username = username;
      user.wallet = walletAddress;
      user.referralCode = referralCode;
      user.role = UserRole.USER;
      user.rank = UserRank.BRONZE;
      user.miningMultiplier = 1;
      user.credits = 100;
      user.path = '';

      // Sử dụng moment.tz để đảm bảo nhất quán múi giờ
      const now = moment().toDate();
      user.createdAt = now;
      user.updatedAt = now;

      if (referrer) {
        user.referredBy = referrer.id;
        user.path = referrer.path ? `${referrer.path}.${referrer.id}` : referrer.id;
      }

      // 2. Lưu người dùng để lấy ID
      const createdUser = await queryRunner.manager.save(User, user);

      // 3. Tạo ví Web3 cho người dùng (không lưu private key thật)
      const wallet = await queryRunner.manager.save(Wallet, {
        userId: createdUser.id,
        address: createdUser.wallet,
        encryptedPrivateKey: createdUser.wallet, // Không lưu private key, chỉ lưu địa chỉ
        type: 'WEB3',
        wmBalance: 0,
        usdtBalance: 0,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });

      // 4-5. Tạo bản ghi số dư token (USDT và WM) cho người dùng
      await Promise.all([
        queryRunner.manager.save(TokenBalance, {
          userId: createdUser.id,
          tokenId: usdtToken.id,
          availableBalance: 0,
          lockedBalance: 0,
          totalBalance: 0,
          createdAt: now,
          updatedAt: now,
        }),
        queryRunner.manager.save(TokenBalance, {
          userId: createdUser.id,
          tokenId: wmToken.id,
          availableBalance: 0,
          lockedBalance: 0,
          totalBalance: 0,
          createdAt: now,
          updatedAt: now,
        })
      ]);

      // 6. Cập nhật ID ví Web3 cho người dùng
      createdUser.web3WalletId = wallet.id;
      createdUser.updatedAt = now;
      const updatedUser = await queryRunner.manager.save(User, createdUser);

      // 7. Xử lý thưởng cho người giới thiệu
      if (referrer && referrer.isKycCompleted) {
        // Kiểm tra số lượng người đã được giới thiệu bởi referrer
        const referralCount = await this.countDirectReferralsKyc(referrer.id);

        // Chỉ thưởng nếu đây là lần giới thiệu đầu tiên hoặc theo chính sách của hệ thống
        if (referralCount.numberDirectReferrals <= 1) { // Giả sử giới hạn 1 người được thưởng
          const referrerWallet = await queryRunner.manager.findOne(Wallet, { where: { userId: referrer.id } });

          const transaction = queryRunner.manager.create(Transaction, {
            walletId: referrerWallet.id,
            userId: referrer.id,
            tokenId: wmToken.id,
            type: TransactionType.TASK_REWARD,
            amount: 1,
            status: TransactionStatus.COMPLETED,
            reference: referrer.id,
            note: `Bonus for first invite friend (+1 WM)`,
            transactionAt: now,
            createdAt: now,
            updatedAt: now,
          });

          const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

          await this.walletService.updateTokenBalanceWithQueryRunner(queryRunner, referrerWallet, wmToken, 'wmBalance', savedTransaction, 1, 'add', now);
        }
      }

      await queryRunner.commitTransaction();

      return updatedUser;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      // Xử lý lỗi chi tiết hơn
      if (error instanceof BadRequestException ||
          error instanceof ConflictException ||
          error instanceof InternalServerErrorException) {
        throw error; // Giữ nguyên lỗi đã xác định
      }

      this.logger.error(`[UsersService][createWithWallet] Lỗi tạo tài khoản: ${error.message || error}`);
      throw new BadRequestException('Failed to create account. Please try again later.');
    } finally {
      await queryRunner.release();
    }
  }

  async getReferralTree(userId: string, maxDepth: number = 5): Promise<TreeNode> {
    const startTime = Date.now();
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) throw new NotFoundException('User not found');

    this.logger.log(`[UsersService] Building referral tree for user ${user.id} ${maxDepth} depth`);
    const tree = await this.buildTreeWithDepth(user, maxDepth);
    const endTime = Date.now();
    this.logger.log(`[UsersService] Referral tree built for user ${user.id} in ${endTime - startTime}ms`);
    return tree;
  }

  async buildTreeWithDepth(rootUser: User, maxDepth: number): Promise<TreeNode> {
    const startTime = Date.now();
    this.logger.log(`[UsersService] Starting buildTreeWithDepth for user ${rootUser.id} with maxDepth ${maxDepth}`);

    // Step 1: Tính prefix
    const startTimePrefix = Date.now();
    const userPrefix = rootUser.path ? `${rootUser.path}.${rootUser.id}` : rootUser.id;
    const endTimePrefix = Date.now();
    this.logger.log(`[UsersService] Step 1: Calculated user prefix '${userPrefix}' in ${endTimePrefix - startTimePrefix}ms`);

    // Step 2: Truy vấn users
    const startTimeQuery = Date.now();
    const users = await this.userRepository
      .createQueryBuilder('user')
      .where('user.id = :rootId OR user.path LIKE :prefix', {
        rootId: rootUser.id,
        prefix: `${userPrefix}%`,
      })
      .orderBy({ 'user.firstDepositTime': 'ASC', 'user.createdAt': 'ASC' })
      .getMany();
    const endTimeQuery = Date.now();
    this.logger.log(`[UsersService] Step 2: Found ${users.length} users in ${endTimeQuery - startTimeQuery}ms`);

    // Step 3: Tạo userMap
    const startTimeMap = Date.now();
    const userMap = new Map<string, User>();
    users.forEach((user) => userMap.set(user.id, user));
    const endTimeMap = Date.now();
    this.logger.log(`[UsersService] Step 3: Created user map with ${userMap.size} entries in ${endTimeMap - startTimeMap}ms`);

    // Step 4: Tính toán
    const startTimeCalculate = Date.now();
    const levelMap = new Map<string, string>();
    const totalReferralsMap = new Map<string, number>();
    const directReferralsMap = new Map<string, number>();
    const parentToChildrenMap = new Map<string, Set<string>>();

    // 4.1: Khởi tạo
    const startTimeInitMaps = Date.now();
    const endTimeInitMaps = Date.now();
    this.logger.log(`[UsersService] Step 4.1: Initialized maps in ${endTimeInitMaps - startTimeInitMaps}ms`);

    // 4.2: Tính level và parent-to-children
    const startTimeLevelAndChildren = Date.now();
    users.forEach((user) => {
      const level = user.path ? user.path.split('.').length : 0;
      levelMap.set(user.id, level.toString());
      if (user.referredBy) {
        if (!parentToChildrenMap.has(user.referredBy)) parentToChildrenMap.set(user.referredBy, new Set());
        parentToChildrenMap.get(user.referredBy)!.add(user.id);
      }
    });
    const endTimeLevelAndChildren = Date.now();
    this.logger.log(`[UsersService] Step 4.2: Calculated levels and built parent-to-children map in ${endTimeLevelAndChildren - startTimeLevelAndChildren}ms`);

    // 4.3: Tính directReferrals
    const startTimeDirectReferrals = Date.now();
    parentToChildrenMap.forEach((children, parentId) => directReferralsMap.set(parentId, children.size));
    const endTimeDirectReferrals = Date.now();
    this.logger.log(`[UsersService] Step 4.3: Calculated directReferrals for ${directReferralsMap.size} users in ${endTimeDirectReferrals - startTimeDirectReferrals}ms`);

    // 4.4: Tính totalReferrals bằng vòng lặp
    const startTimeTotalReferrals = Date.now();
    const calculateTotalReferrals = () => {
      const queue = [rootUser.id];
      const visited = new Set<string>();
      const referralCounts = new Map<string, number>();
      users.forEach((user) => referralCounts.set(user.id, 0));

      while (queue.length > 0) {
        const currentId = queue.shift()!;
        if (visited.has(currentId)) continue;
        visited.add(currentId);

        const children = parentToChildrenMap.get(currentId);
        if (children) {
          children.forEach((childId) => {
            if (!visited.has(childId)) {
              queue.push(childId);
              referralCounts.set(currentId, (referralCounts.get(currentId) || 0) + 1);
              const childReferrals = referralCounts.get(childId) || 0;
              referralCounts.set(currentId, (referralCounts.get(currentId) || 0) + childReferrals);
            }
          });
        }
      }
      referralCounts.forEach((count, userId) => totalReferralsMap.set(userId, count));
    };
    calculateTotalReferrals();
    const endTimeTotalReferrals = Date.now();
    this.logger.log(`[UsersService] Step 4.4: Calculated totalReferrals for ${totalReferralsMap.size} users in ${endTimeTotalReferrals - startTimeTotalReferrals}ms`);

    const endTimeCalculate = Date.now();
    this.logger.log(`[UsersService] Step 4: Completed all calculations in ${endTimeCalculate - startTimeCalculate}ms`);

    // Step 5: Xây dựng cây với lazy-load
    const startTimeBuild = Date.now();

    // 5.1: Tạo root node
    const startTimeRootNode = Date.now();
    const rootNode = this.createTreeNode(rootUser, levelMap, totalReferralsMap, directReferralsMap, maxDepth, users);
    const endTimeRootNode = Date.now();
    this.logger.log(`[UsersService] Step 5.1: Created root node in ${endTimeRootNode - startTimeRootNode}ms`);

    // 5.2: Xây dựng cây từ root xuống với độ sâu giới hạn
    const startTimeBuildTree = Date.now();
    const nodes = new Map<string, TreeNode>([[rootUser.id, rootNode]]);
    const queue = [{ id: rootUser.id, depth: 0 }];

    while (queue.length > 0) {
      const { id, depth } = queue.shift()!;
      if (depth >= maxDepth) continue;

      const childrenIds = parentToChildrenMap.get(id);
      if (childrenIds) {
        childrenIds.forEach((childId) => {
          const childUser = userMap.get(childId);
          if (childUser) {
            const childNode = this.createTreeNode(childUser, levelMap, totalReferralsMap, directReferralsMap, maxDepth, users);
            nodes.set(childId, childNode);
            nodes.get(id)!.children.push(childNode);
            queue.push({ id: childId, depth: depth + 1 });
          }
        });
      }
    }
    const endTimeBuildTree = Date.now();
    this.logger.log(`[UsersService] Step 5.2: Constructed tree with ${nodes.size} nodes at depth ${maxDepth} in ${endTimeBuildTree - startTimeBuildTree}ms`);

    const endTimeBuild = Date.now();
    this.logger.log(`[UsersService] Step 5: Completed tree construction in ${endTimeBuild - startTimeBuild}ms`);

    const endTime = Date.now();
    this.logger.log(`[UsersService] Total time for buildTreeWithDepth: ${endTime - startTime}ms`);

    return rootNode;
  }

  createTreeNode(
    user: User,
    levelMap: Map<string, string>,
    totalReferralsMap: Map<string, number>,
    directReferralsMap: Map<string, number>,
    maxDepth: number,
    allUsers: User[]
  ): TreeNode {
    const level = parseInt(levelMap.get(user.id) || "0");
    const hasMoreChildren = level < maxDepth && (directReferralsMap.get(user.id) || 0) > 0;

    return {
      id: user.id,
      name: user.name,
      username: user.username,
      walletAddress: user.wallet,
      rank: user.rank,
      wManagerRank: user.wManagerRank,
      totalMined: user.totalMined,
      totalEarnings: user.totalEarnings,
      totalVolume: user.totalVolume,
      totalVolumePerDay: user.totalVolumePerDay,
      totalEarningsPerDay: user.totalEarningsPerDay,
      totalVolumeSession: user.totalVolumeSession,
      totalEarningsSession: user.totalEarningsSession,
      totalSellVolume: user.totalSellVolume,
      totalSellVolumePerDay: user.totalSellVolumePerDay,
      totalSellVolumeSession: user.totalSellVolumeSession,
      hasF1BuySession: user.hasF1BuySession,
      hasF1SellSession: user.hasF1SellSession,
      level: levelMap.get(user.id) || "0",
      totalReferrals: totalReferralsMap.get(user.id) || 0,
      totalReferralsKyc: totalReferralsMap.get(user.id) || 0,
      directReferrals: directReferralsMap.get(user.id) || 0,
      directReferralsKyc: directReferralsMap.get(user.id) || 0,
      firstDepositTime: user.firstDepositTime ? user.firstDepositTime.toISOString() : null,
      joinedAt: user.createdAt.toISOString(),
      hasLightningBolt: user.hasLightningBolt,
      children: [],
      hasMoreChildren,
    };
  }

  // API để lấy tầng con khi cần
  async getChildren(userId: string): Promise<TreeNode[]> {
    const startTime = Date.now();
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) return [];

    // Lấy danh sách con trực tiếp
    const startTimeQuery = Date.now();
    const children = await this.userRepository.find({
      where: { referredBy: userId },
      order: { firstDepositTime: 'ASC', createdAt: 'ASC' },
    });
    const endTimeQuery = Date.now();
    this.logger.log(`[UsersService] Fetched ${children.length} direct children for user ${userId} in ${endTimeQuery - startTimeQuery}ms`);

    // Tính toán thông tin cho các con
    const levelMap = new Map<string, string>();
    const totalReferralsMap = new Map<string, number>();
    const directReferralsMap = new Map<string, number>();
    const parentToChildrenMap = new Map<string, Set<string>>();

    const startTimeCalculate = Date.now();
    children.forEach((child) => {
      const level = child.path ? child.path.split('.').length : 0;
      levelMap.set(child.id, level.toString());

      // Xây dựng parent-to-children map
      if (child.referredBy) {
        if (!parentToChildrenMap.has(child.referredBy)) parentToChildrenMap.set(child.referredBy, new Set());
        parentToChildrenMap.get(child.referredBy)!.add(child.id);
      }
    });

    // Tính directReferrals
    parentToChildrenMap.forEach((childrenIds, parentId) => {
      directReferralsMap.set(parentId, childrenIds.size);
    });

    // Tính totalReferrals
    children.forEach((child) => {
      const queue = [child.id];
      const visited = new Set<string>();
      let totalReferrals = 0;

      while (queue.length > 0) {
        const currentId = queue.shift()!;
        if (visited.has(currentId)) continue;
        visited.add(currentId);

        const childChildren = parentToChildrenMap.get(currentId);
        if (childChildren) {
          totalReferrals += childChildren.size;
          childChildren.forEach((childId) => queue.push(childId));
        }
      }
      totalReferralsMap.set(child.id, totalReferrals);
    });
    const endTimeCalculate = Date.now();
    this.logger.log(`[UsersService] Calculated data for children in ${endTimeCalculate - startTimeCalculate}ms`);

    // Tạo nodes
    const startTimeBuildNodes = Date.now();
    const nodes = children.map((child) =>
      this.createTreeNode(child, levelMap, totalReferralsMap, directReferralsMap, Infinity, children)
    );
    const endTimeBuildNodes = Date.now();
    this.logger.log(`[UsersService] Built ${nodes.length} child nodes in ${endTimeBuildNodes - startTimeBuildNodes}ms`);

    const endTime = Date.now();
    this.logger.log(`[UsersService] Fetched ${nodes.length} children for user ${userId} in ${endTime - startTime}ms`);
    return nodes;
  }

  async calculateLevel(user: User): Promise<string> {
    // Nếu path là null thì đây là F0, level = 0
    if (!user.path) {
      return "0";
    }

    // Tách chuỗi path bằng dấu chấm và đếm số phần tử
    // Ví dụ: "F0.F1.F2" -> ["F0", "F1", "F2"] -> length = 3
    const pathSegments = user.path.split('.');
    const level = pathSegments.length;

    return level.toString();
  }

  async countDirectReferralsKyc(userId: string): Promise<{ numberDirectReferrals: number, numberDirectReferralsKyc: number }> {
    let numberDirectReferrals = 0;
    let numberDirectReferralsKyc = 0;

    const referrals: User[] = await this.userRepository
      .createQueryBuilder('user')
      .where('user.referredBy = :userId', { userId })
      .getMany();

    numberDirectReferrals = referrals.length;
    numberDirectReferralsKyc = referrals.filter(referral => referral.isKycCompleted).length;

    return { numberDirectReferrals, numberDirectReferralsKyc };
  }

  async countAllReferrals(userId: string): Promise<{
    totalDirectReferrals: number;
    totalDirectReferralsKyc: number;
    totalReferrals: number;
    totalReferralsKyc: number;
  }> {
    // Count all referrals in tree
    const allResult = await this.userRepository
    .createQueryBuilder('user')
    .select([
      'COUNT(*) as totalReferrals',
      'SUM(CASE WHEN user.isKycCompleted = true THEN 1 ELSE 0 END) as totalReferralsKyc'
    ])
    .where(new Brackets(qb => {
      qb.where('user.path = :exactPath', { exactPath: userId })  // Exact match
        .orWhere('user.path LIKE :startPath', { startPath: `${userId}.%` })  // userId at start
        .orWhere('user.path LIKE :middlePath', { middlePath: `%.${userId}.%` })  // userId in middle
        .orWhere('user.path LIKE :endPath', { endPath: `%.${userId}` });  // userId at end
    }))
    .andWhere('user.id != :userId', { userId })  // Exclude self
    .andWhere('user.isRoot = :isRoot', { isRoot: false })  // Exclude root users if any
    .getRawOne();

    // Count direct referrals
    const directResult = await this.userRepository
      .createQueryBuilder('user')
      .select([
        'COUNT(*) as totalDirectReferrals',
        'SUM(CASE WHEN user.isKycCompleted = true THEN 1 ELSE 0 END) as totalDirectReferralsKyc'
      ])
      .where('user.referredBy = :userId', { userId })
      .getRawOne();

    return {
      totalDirectReferrals: parseInt(directResult.totalDirectReferrals) || 0,
      totalDirectReferralsKyc: parseInt(directResult.totalDirectReferralsKyc) || 0,
      totalReferrals: parseInt(allResult.totalReferrals) || 0,
      totalReferralsKyc: parseInt(allResult.totalReferralsKyc) || 0
    };
  }

  async countDirectReferrals(tree: TreeNode): Promise<number> {
    return tree.children.length;
  }

  async getTotalVolumeAndEarnings(userId: string): Promise<{ totalVolume: number, totalVolumePerDay: number, totalEarnings: number, totalEarningsPerDay: number, totalVolumeSession: number, totalEarningsSession: number, totalSellVolume: number, totalSellVolumePerDay: number, totalSellVolumeSession: number, hasF1BuySession: boolean, hasF1SellSession: boolean }> {
    let totalVolume = 0;
    let totalVolumePerDay = 0;
    let totalVolumeSession = 0;

    let totalSellVolume = 0;
    let totalSellVolumePerDay = 0;
    let totalSellVolumeSession = 0;

    let totalEarnings = 0;
    let totalEarningsPerDay = 0;
    let totalEarningsSession = 0;

    let hasF1BuySession = false;
    let hasF1SellSession = false;

    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    totalVolume = Number(user.totalVolume);
    totalVolumePerDay = Number(user.totalVolumePerDay);
    totalVolumeSession = Number(user.totalVolumeSession);

    totalSellVolume = Number(user.totalSellVolume);
    totalSellVolumePerDay = Number(user.totalSellVolumePerDay);
    totalSellVolumeSession = Number(user.totalSellVolumeSession);

    totalEarnings = Number(user.totalEarnings);
    totalEarningsPerDay = Number(user.totalEarningsPerDay);
    totalEarningsSession = Number(user.totalEarningsSession);

    hasF1BuySession = user.hasF1BuySession;
    hasF1SellSession = user.hasF1SellSession;

    return { totalVolume, totalVolumePerDay, totalEarnings, totalEarningsPerDay, totalVolumeSession, totalEarningsSession, totalSellVolume, totalSellVolumePerDay, totalSellVolumeSession, hasF1BuySession, hasF1SellSession };
  }

  async getDirectReferrals(userId: string, tree: TreeNode): Promise<TreeNode[]> {
    try {
      const referrals = await this.userRepository.find({
        where: { referredBy: userId },
        order: {
          firstDepositTime: 'DESC',
          createdAt: 'DESC'
        }
      });

      // Tạo Map từ cây để tra cứu nhanh
      const treeMap = new Map<string, TreeNode>();
      const buildTreeMap = (nodes: TreeNode) => {
        treeMap.set(nodes.id, nodes);
        if (nodes.children.length > 0) {
          for (const child of nodes.children) {
            buildTreeMap(child);
          }
        }
      };
      buildTreeMap(tree);

      // Hàm tính totalReferrals từ cây
      const calculateTotalReferrals = (node: TreeNode): number => {
        let total = node.directReferrals; // Số referrals trực tiếp
        for (const child of node.children) {
          total += calculateTotalReferrals(child); // Cộng dồn từ các referrals gián tiếp
        }
        return total;
      };

      const directReferrals: TreeNode[] = [];
      for (const referral of referrals) {
        const level = await this.calculateLevel(referral);
        const treeNode = treeMap.get(referral.id);
        const totalReferrals = treeNode ? calculateTotalReferrals(treeNode) : 0;
        const directReferralsCount = (await this.userRepository.find({ where: { referredBy: referral.id } })).length;

        directReferrals.push({
          id: referral.id,
          name: referral.name,
          username: referral.username,
          walletAddress: referral.wallet,
          rank: referral.rank,
          wManagerRank: referral.wManagerRank,
          totalMined: referral.totalMined,
          totalEarnings: referral.totalEarnings,
          totalVolume: referral.totalVolume,
          totalVolumePerDay: referral.totalVolumePerDay,
          totalEarningsPerDay: referral.totalEarningsPerDay,
          totalVolumeSession: referral.totalVolumeSession,
          totalEarningsSession: referral.totalEarningsSession,
          totalSellVolume: referral.totalSellVolume,
          totalSellVolumePerDay: referral.totalSellVolumePerDay,
          totalSellVolumeSession: referral.totalSellVolumeSession,
          hasF1BuySession: referral.hasF1BuySession,
          hasF1SellSession: referral.hasF1SellSession,
          level: level,
          totalReferrals: totalReferrals,
          totalReferralsKyc: totalReferrals,
          directReferrals: directReferralsCount,
          directReferralsKyc: directReferralsCount,
          firstDepositTime: referral.firstDepositTime ? referral.firstDepositTime.toISOString() : null,
          joinedAt: referral.createdAt.toISOString(),
          hasLightningBolt: referral.hasLightningBolt,
          children: [],
        });
      }

      return directReferrals;
    } catch (error) {
      console.error('Error getting direct referrals:', error);
      return [];
    }
  }

  // NFT Trading Related Methods
  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['web3Wallet']
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async checkUserBalances(userId: string): Promise<{ wmBalance: number, usdtBalance: number }> {
    const user = await this.findById(userId);
    if (!user.web3Wallet) {
      throw new BadRequestException('User has no Web3 wallet');
    }

    const [wmBalance, usdtBalance] = await Promise.all([
      this.walletService.getTokenBalance(user.web3Wallet.id, 'WM'),
      this.walletService.getTokenBalance(user.web3Wallet.id, 'USDT')
    ]);

    return {
      wmBalance,
      usdtBalance
    };
  }

  async updateUserBalances(userId: string, wmDelta: number = 0, usdtDelta: number = 0): Promise<void> {
    const user = await this.findById(userId);
    if (!user.web3Wallet) {
      throw new BadRequestException('User has no Web3 wallet');
    }

    //const wmToken = await this.tokenService.findBySymbol('WM');
    //const usdtToken = await this.tokenService.findBySymbol('USDT');

    if (wmDelta !== 0) {
      // await this.walletService.updateTokenBalance(user.web3Wallet.id, wmToken, wmDelta);
    }

    if (usdtDelta !== 0) {
      // await this.walletService.updateTokenBalance(user.web3Wallet.id, usdtToken, usdtDelta);
    }
  }

  async validateUserForNFTPurchase(userId: string, requiredWM: number, requiredUSDT: number): Promise<boolean> {
    const { wmBalance, usdtBalance } = await this.checkUserBalances(userId);

    if (wmBalance < requiredWM) {
      throw new BadRequestException(`Insufficient WM balance. Required: ${requiredWM}, Available: ${wmBalance}`);
    }

    if (usdtBalance < requiredUSDT) {
      throw new BadRequestException(`Insufficient USDT balance. Required: ${requiredUSDT}, Available: ${usdtBalance}`);
    }

    return true;
  }

  async processNFTPurchaseBalances(
    buyerId: string,
    sellerId: string | null,
    nftPrice: number,
    gasFee: number
  ): Promise<void> {
    // Start a transaction to handle balance updates
    const queryRunner = this.userRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Deduct WM for gas fee from buyer
      await this.updateUserBalances(buyerId, -gasFee, 0);

      // Deduct USDT for NFT price from buyer
      await this.updateUserBalances(buyerId, 0, -nftPrice);

      // Add USDT to seller if exists
      if (sellerId) {
        await this.updateUserBalances(sellerId, 0, nftPrice);
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(`Failed to process NFT purchase: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async getUserPurchaseHistory(userId: string): Promise<any[]> {
    const user = await this.findById(userId);
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.nftOrders', 'orders')
      .where('orders.buyerId = :userId', { userId: user.id })
      .orderBy('orders.createdAt', 'DESC')
      .getMany();
  }

  async getUserSalesHistory(userId: string): Promise<any[]> {
    const user = await this.findById(userId);
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.nftSales', 'sales')
      .where('sales.sellerId = :userId', { userId: user.id })
      .orderBy('sales.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tạo báo cáo tài chính đầy đủ cho người dùng
   * @param userId ID của người dùng cần tạo báo cáo
   * @returns Báo cáo tài chính đầy đủ
   */
  async getFinancialReport(userId: string): Promise<FinancialReportDto> {
    this.logger.log(`[getFinancialReport] Tạo báo cáo tài chính cho người dùng ${userId}`);

    try {
      // Thiết lập cte_max_recursion_depth trong một câu lệnh riêng
      await this.dataSource.query(`SET SESSION cte_max_recursion_depth = 10000`);

      // Câu lệnh SQL để tạo báo cáo tài chính - sử dụng tham số trực tiếp thay vì biến session
      const financialReportQuery = `
        WITH FinalAdjustedData AS (
          WITH RECURSIVE BaseData AS (
            -- Bước 0: Chuẩn bị dữ liệu gốc và thêm số thứ tự
            SELECT
                t.id,
                t.userId,
                DATE(t.createdAt) AS createdDate,
                t.createdAt,
                t.type,
                t.amount,
                CASE WHEN t.type IN(
                    'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION',
                    'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST',
                    'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING',
                    'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
                    'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER'
                ) THEN t.amount ELSE 0 END AS original_cash_in,
                CASE WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE')
                     THEN -ABS(t.amount) ELSE 0 END AS original_cash_out,
                CASE WHEN t.type = 'NFT_PURCHASE' THEN ABS(t.amount) ELSE 0 END AS potential_purchase_value,
                CASE
                    WHEN t.type = 'NFT_SALE' THEN ROUND(ABS(t.amount) / 1.025, 8)
                    ELSE 0
                END AS base_purchase_value_equivalent,
                ROW_NUMBER() OVER (PARTITION BY t.userId ORDER BY t.createdAt ASC, t.id ASC) as rn
            FROM
                transactions t
            WHERE
                t.status = 'COMPLETED'
                AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5'
                AND t.userId = ?
          ),
          -- CTE này tính toán original_cash_total để JOIN lại ở cuối
          OriginalCalculations AS (
               SELECT
                  t.id,
                  t.userId,
                  -- Tính cash_total gốc (theo logic ban đầu user cung cấp)
                  SUM(
                      CASE
                          WHEN t.type IN(
                              'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION',
                              'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST',
                              'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING',
                              'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK',
                              'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER'
                          ) THEN t.amount
                          WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE')
                          THEN -ABS(t.amount)
                          ELSE 0
                      END
                  ) OVER(
                      PARTITION BY t.userId ORDER BY t.createdAt ASC, t.id ASC
                      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                  ) AS original_cash_total,
                  -- Lấy original_cash_in/out để hiển thị lại
                  CASE WHEN t.type IN( 'DEPOSIT', 'KYC_VERIFY_COMMISSION', 'NFT_SALE', 'DIRECT_COMMISSION', 'RANKING_COMMISSION', 'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST', 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK', 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER' ) THEN t.amount ELSE 0 END AS original_cash_in,
                  CASE WHEN t.type IN('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') THEN -ABS(t.amount) ELSE 0 END AS original_cash_out
              FROM
                  transactions t
              WHERE
                  t.status = 'COMPLETED'
                  AND t.tokenId = 'cbea6a1f-5a1f-4a0d-ae90-d008172eaca5'
                  AND t.userId = ?
          ),
          AdjustedFlowSimulation (
              id, userId, createdDate, createdAt, type, amount,
              original_cash_in_base, original_cash_out_base,
              potential_purchase_value, base_purchase_value_equivalent, rn,
              transaction_validity_status,
              valid_purchase_value,
              adjusted_cash_in,
              adjusted_cash_out,
              cumulative_valid_purchase_value,
              cumulative_sale_equivalent_value,
              adjusted_cash_total
          ) AS (
              -- ================= ANCHOR MEMBER (rn=1) =================
              SELECT
                  bd.id, bd.userId, bd.createdDate, bd.createdAt, bd.type, bd.amount,
                  bd.original_cash_in, bd.original_cash_out,
                  bd.potential_purchase_value, bd.base_purchase_value_equivalent, bd.rn,
                  -- 1.1: Status - Bổ sung GLOBAL_MINING_COSHARE
                  CASE
                      WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID_OUTFLOW_INSUFFICIENT_FUNDS'
                      WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID_SALE_NO_VALID_PURCHASE_AVAILABLE'
                      ELSE 'NORMAL'
                  END AS transaction_validity_status,
                  -- 1.2: Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
                  CAST(CASE
                      WHEN bd.type = 'NFT_PURCHASE' AND
                           (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
                      THEN bd.potential_purchase_value
                      ELSE 0.0
                  END AS DECIMAL(65, 8)) AS valid_purchase_value,
                  -- 1.3: Adjusted Cash In - Logic kiểm tra outflow bên trong cần cập nhật
                  CAST(CASE
                      WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
                      THEN bd.original_cash_in ELSE 0.0
                  END AS DECIMAL(65, 8)) AS adjusted_cash_in,
                  -- 1.4: Adjusted Cash Out - Logic kiểm tra outflow bên trong cần cập nhật
                  CAST(CASE
                      WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
                      THEN bd.original_cash_out ELSE 0.0
                  END AS DECIMAL(65, 8)) AS adjusted_cash_out,
                  -- 1.5: Cumulative Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
                  CAST(CASE
                      WHEN bd.type = 'NFT_PURCHASE' AND
                           (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
                      THEN bd.potential_purchase_value
                      ELSE 0.0
                  END AS DECIMAL(65, 8)) AS cumulative_valid_purchase_value,
                  -- 1.6: Cumulative Sale Equivalent Value (Không thay đổi logic này vì chỉ liên quan đến NFT_SALE)
                  CAST(CASE
                       WHEN bd.type = 'NFT_SALE' AND
                            (CASE WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
                       THEN bd.base_purchase_value_equivalent
                       ELSE 0.0
                  END AS DECIMAL(65, 8)) AS cumulative_sale_equivalent_value,
                  -- 1.7: Adjusted Cash Total - Logic kiểm tra outflow bên trong cần cập nhật
                  CAST(
                     (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_in ELSE 0.0 END)
                   + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (CAST(0 AS DECIMAL(65,8)) + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND bd.base_purchase_value_equivalent > 0 THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_out ELSE 0.0 END)
                    AS DECIMAL(65, 8)) AS adjusted_cash_total
              FROM BaseData bd
              WHERE bd.rn = 1

              UNION ALL

              -- ================= RECURSIVE MEMBER (rn > 1) =================
              SELECT
                  bd.id, bd.userId, bd.createdDate, bd.createdAt, bd.type, bd.amount,
                  bd.original_cash_in, bd.original_cash_out,
                  bd.potential_purchase_value, bd.base_purchase_value_equivalent, bd.rn,
                  -- 2.1: Status - Bổ sung GLOBAL_MINING_COSHARE
                  CASE
                      WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID_OUTFLOW_INSUFFICIENT_FUNDS'
                      WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID_SALE_NO_VALID_PURCHASE_AVAILABLE'
                      ELSE 'NORMAL'
                  END AS transaction_validity_status,
                  -- 2.2: Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
                  CASE
                      WHEN bd.type = 'NFT_PURCHASE' AND
                           (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
                      THEN bd.potential_purchase_value
                      ELSE 0.0
                  END AS valid_purchase_value,
                  -- 2.3: Adjusted Cash In - Logic kiểm tra outflow và sale bên trong cần cập nhật
                  CASE
                      WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
                      THEN bd.original_cash_in ELSE 0.0
                  END AS adjusted_cash_in,
                  -- 2.4: Adjusted Cash Out - Logic kiểm tra outflow và sale bên trong cần cập nhật
                  CASE
                      WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL'
                      THEN bd.original_cash_out ELSE 0.0
                  END AS adjusted_cash_out,
                  -- 2.5: Cumulative Valid Purchase Value - Logic kiểm tra outflow bên trong cần cập nhật
                  prev.cumulative_valid_purchase_value +
                  (CASE WHEN bd.type = 'NFT_PURCHASE' AND (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.potential_purchase_value ELSE 0.0 END)
                  AS cumulative_valid_purchase_value,
                  -- 2.6: Cumulative Sale Equivalent Value (Không thay đổi logic này)
                  prev.cumulative_sale_equivalent_value +
                  (CASE WHEN bd.type = 'NFT_SALE' AND (CASE WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.base_purchase_value_equivalent ELSE 0.0 END)
                  AS cumulative_sale_equivalent_value,
                  -- 2.7: Adjusted Cash Total - Logic kiểm tra outflow và sale bên trong cần cập nhật
                  prev.adjusted_cash_total
                   + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_in ELSE 0.0 END)
                   + (CASE WHEN (CASE WHEN bd.type IN ('NFT_PURCHASE', 'WITHDRAWAL', 'GLOBAL_MINING_COSHARE') AND (prev.adjusted_cash_total + bd.original_cash_out < 0) THEN 'INVALID' WHEN bd.type = 'NFT_SALE' AND ROUND(prev.cumulative_sale_equivalent_value + bd.base_purchase_value_equivalent, 8) > ROUND(prev.cumulative_valid_purchase_value, 8) THEN 'INVALID' ELSE 'NORMAL' END) = 'NORMAL' THEN bd.original_cash_out ELSE 0.0 END)
                  AS adjusted_cash_total
              FROM BaseData bd
              INNER JOIN AdjustedFlowSimulation prev ON bd.userId = prev.userId AND bd.rn = prev.rn + 1
          ),
          -- Bước bổ sung: Tìm tổng bán cuối cùng VÀ tính luôn trạng thái purchase_processing_status
          FinalMarking AS (
              SELECT
                  afs.*,
                  -- Tìm giá trị cumulative_sale_equivalent_value lớn nhất (tức là giá trị cuối cùng) cho user
                  MAX(afs.cumulative_sale_equivalent_value) OVER (PARTITION BY afs.userId) as final_cumulative_sale_equivalent,
                  -- Tính luôn cột trạng thái ở đây
                  CASE
                      WHEN afs.type = 'NFT_PURCHASE'
                       AND afs.transaction_validity_status = 'NORMAL'
                       -- So sánh với giá trị MAX OVER được tính ngay trong CTE này
                       AND ROUND(afs.cumulative_valid_purchase_value, 8) > ROUND(MAX(afs.cumulative_sale_equivalent_value) OVER (PARTITION BY afs.userId), 8)
                      THEN 'UNPROCESSED'
                      ELSE NULL
                  END AS purchase_processing_status
              FROM AdjustedFlowSimulation afs
          )
          -- Final Select: Kết hợp với original_cash_total và sắp xếp
          SELECT
              fm.id, fm.userId, fm.createdDate, fm.createdAt, fm.type, fm.amount,
              oc.original_cash_in, oc.original_cash_out, oc.original_cash_total,
              fm.transaction_validity_status,
              fm.adjusted_cash_in,
              fm.adjusted_cash_out,
              fm.adjusted_cash_total,
              fm.purchase_processing_status -- Chọn cột đã được tính trong FinalMarking
          FROM FinalMarking fm
          JOIN OriginalCalculations oc ON fm.id = oc.id
        ),
        -- Bước cuối: Tổng hợp từ dữ liệu đã điều chỉnh
        SummaryCalculations AS (
            SELECT
            userId,

            -- 1. Tổng Nạp
            SUM(CASE WHEN type = 'DEPOSIT' THEN 1 ELSE 0 END) AS deposit_count,
            SUM(CASE WHEN type = 'DEPOSIT' THEN adjusted_cash_in ELSE 0 END) AS deposit_total_amount,

            -- 2. Tổng Bán (Hợp Lệ)
            SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN 1 ELSE 0 END) AS valid_sale_count,
            SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN adjusted_cash_in ELSE 0 END) AS valid_sale_total_amount,

            -- 3. Tổng Mua (Hợp Lệ)
            SUM(CASE WHEN type = 'NFT_PURCHASE' AND transaction_validity_status = 'NORMAL' THEN 1 ELSE 0 END) AS valid_purchase_count,
            SUM(CASE WHEN type = 'NFT_PURCHASE' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END) AS valid_purchase_total_amount, -- Lấy giá trị dương

            -- 4. Lãi NFT THỰC TẾ
            SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN adjusted_cash_in ELSE 0 END) - SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN adjusted_cash_in / 1.025 ELSE 0 END) AS actual_nft_profit,

            -- 4b. Tổng giá trị mua hợp lệ CHƯA được bán
            (SUM(CASE WHEN type = 'NFT_PURCHASE' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END) - SUM(CASE WHEN type = 'NFT_SALE' AND transaction_validity_status = 'NORMAL' THEN adjusted_cash_in / 1.025 ELSE 0 END)) AS unprocessed_valid_purchase_value,

            -- 5. Tổng Rút
            SUM(CASE WHEN type = 'WITHDRAWAL' THEN 1 ELSE 0 END) AS actual_withdrawal_count, -- Đếm tất cả lệnh rút
            SUM(CASE WHEN type = 'WITHDRAWAL' THEN -original_cash_out ELSE 0 END) AS actual_withdrawal_total_amount, -- Tổng tiền rút thực tế (từ original_cash_out)
            SUM(CASE WHEN type = 'WITHDRAWAL' AND transaction_validity_status = 'NORMAL' THEN 1 ELSE 0 END) AS valid_withdrawal_count, -- Đếm lệnh rút hợp lệ
            SUM(CASE WHEN type = 'WITHDRAWAL' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END) AS valid_withdrawal_total_amount, -- Tổng tiền rút hợp lệ

            -- 5b. (Mới) Tổng GLOBAL_MINING_COSHARE (Hợp Lệ)
            SUM(CASE WHEN type = 'GLOBAL_MINING_COSHARE' AND transaction_validity_status = 'NORMAL' THEN 1 ELSE 0 END) AS valid_global_mining_coshare_count,
            SUM(CASE WHEN type = 'GLOBAL_MINING_COSHARE' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END) AS valid_global_mining_coshare_total_amount, -- Lấy giá trị dương

            -- 6. Tổng Hoa Hồng
            SUM(CASE WHEN type IN ('KYC_VERIFY_COMMISSION', 'DIRECT_COMMISSION', 'RANKING_COMMISSION', 'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST', 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK', 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER') THEN 1 ELSE 0 END) AS commission_count,
            SUM(CASE WHEN type IN ('KYC_VERIFY_COMMISSION', 'DIRECT_COMMISSION', 'RANKING_COMMISSION', 'CO_SHAREHOLDER_BONUS', 'SYSTEM_COMMISSION', 'GLOBAL_MINING_COSHARE_INTEREST', 'GLOBAL_MINING_COSHARE_COMMISSION_DIRECT', 'GLOBAL_MINING_COSHARE_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_PROFIT_COMMISSION_MATCHING', 'GLOBAL_MINING_COSHARE_COMMISSION_RANK', 'GLOBAL_MINING_COSHARE_COMMISSION_SHAREHOLDER') THEN adjusted_cash_in ELSE 0 END) AS commission_total_amount,

            -- 7. Số dư cuối cùng (Cách tính không đổi, vẫn bao gồm tất cả dòng tiền hợp lệ)
            SUM(adjusted_cash_in + adjusted_cash_out) AS final_adjusted_balance,

            -- 8. (Mới) Số dư cuối cùng SAU KHI trừ tất cả các lần RÚT THỰC TẾ
            (SUM(adjusted_cash_in + adjusted_cash_out) -- Lấy số dư cuối hợp lệ
             - (SUM(CASE WHEN type = 'WITHDRAWAL' THEN -original_cash_out ELSE 0 END) -- Trừ tổng rút thực tế
                - SUM(CASE WHEN type = 'WITHDRAWAL' AND transaction_validity_status = 'NORMAL' THEN -adjusted_cash_out ELSE 0 END)) -- Cộng lại phần rút hợp lệ đã bị trừ trong final_adjusted_balance
            ) AS balance_after_actual_withdrawals

        FROM FinalAdjustedData
        GROUP BY userId
        ),
        -- THÊM CÁC CTE LẤY THÔNG TIN HIỆN TẠI
        UserInfo AS (
            SELECT id, referralCode, wallet, isKycCompleted, name, phone, email
            FROM users
            WHERE id = ?
        ),
        WalletInfo AS (
            SELECT userId, usdtBalance
            FROM wallets
            WHERE userId = ?
        ),
        CurrentNfts AS (
            SELECT
                owner_id,
                COALESCE(SUM(currentPrice * quantity), 0) AS total_hold_nft_value,
                COALESCE(GROUP_CONCAT(CONCAT(type, ' (Qty: ', quantity, ')') ORDER BY type SEPARATOR ', '), 'Không có') AS owned_nft_summary
            FROM nfts
            WHERE owner_id = ?
            GROUP BY owner_id
        ),
        PendingNftOrders AS (
            SELECT
                traderId,
                COUNT(*) AS pending_order_count,
                COALESCE(SUM(nftPrice * quantity), 0) AS total_pending_nft_price_value,
                COALESCE(GROUP_CONCAT(CONCAT(orderType, ' ', quantity, ' ', nftType) ORDER BY createdAt SEPARATOR '; '), 'Không có') AS pending_order_details
            FROM nft_orders
            WHERE traderId = ?
              AND status = 'PENDING'
            GROUP BY traderId
        ),
        PendingWithdrawals AS (
            SELECT
                userId,
                COUNT(*) AS pending_withdrawal_count,
                COALESCE(SUM(ABS(amount)), 0) AS pending_withdrawal_amount -- Lấy tổng dương
            FROM transactions
            WHERE userId = ?
              AND type = 'WITHDRAWAL'
              AND status = 'PENDING'
            GROUP BY userId
        )
        -- THAY ĐỔI SELECT CUỐI CÙNG ĐỂ TẠO BÁO CÁO
        SELECT
            CONCAT(
                'FINANCIAL AUDIT REPORT:\\n\\n',
                'Name: ', COALESCE(ui.name, 'N/A'), '\\n',
                'Email: ', COALESCE(ui.email, 'N/A'), '\\n',
                'Phone: ', COALESCE(ui.phone, 'N/A'), '\\n',
                'Wallet (BNB): ', COALESCE(ui.wallet, 'N/A'), '\\n',
                'Referral Code: ', COALESCE(ui.referralCode, 'N/A'), '\\n',
                'KYC Status: ', IF(ui.isKycCompleted = 1, 'Completed', 'None'), '\\n\\n',
                'System Status:\\n',
                'Wallet Balance (USDT): ', FORMAT(COALESCE(wi.usdtBalance, 0), 8), '\\n',
                'NFT Holdings: ', COALESCE(cn.owned_nft_summary, 'None'), '\\n',
                '   * Total Value (Current Price): ', FORMAT(COALESCE(cn.total_hold_nft_value, 0), 8), '\\n',
                'Pending NFT Orders: ', COALESCE(pno.pending_order_details, 'None'), ' (', COALESCE(pno.pending_order_count, 0), ')\\n',
                '   * Total Value (nftPrice): ', FORMAT(COALESCE(pno.total_pending_nft_price_value, 0), 8), '\\n',
                'Pending Withdrawals: ', FORMAT(COALESCE(pw.pending_withdrawal_amount, 0), 8), ' (', COALESCE(pw.pending_withdrawal_count, 0), ')\\n\\n',
                'Audit Details:\\n',
                '1. Deposits: ', COALESCE(cs.deposit_count, 0), ' txn, total ', FORMAT(COALESCE(cs.deposit_total_amount, 0), 8), '\\n',
                '2. Withdrawals: ', COALESCE(cs.actual_withdrawal_count, 0), ' txn, total ', FORMAT(COALESCE(cs.actual_withdrawal_total_amount, 0), 8), '\\n',
                '   (Valid: ', COALESCE(cs.valid_withdrawal_count, 0), ' txn, total ', FORMAT(COALESCE(cs.valid_withdrawal_total_amount, 0), 8), ')\\n',
                '3. NFT Profit (Net): ', FORMAT(COALESCE(cs.actual_nft_profit, 0), 8), '\\n',
                '   *(Valid ', COALESCE(cs.valid_sale_count, 0), ' SELL: ', FORMAT(COALESCE(cs.valid_sale_total_amount, 0), 8), ', BUY: ', FORMAT(COALESCE(cs.valid_sale_total_amount, 0) / 1.025, 8), ')*\\n',
                '4. Commissions: ', COALESCE(cs.commission_count, 0), ' txn, total ', FORMAT(COALESCE(cs.commission_total_amount, 0), 8), '\\n',
                '5. NFT Inventory (Cost): ', FORMAT(COALESCE(cs.unprocessed_valid_purchase_value, 0), 8), '\\n',
                '   *(Valid ', COALESCE(cs.valid_purchase_count, 0), ' txn, Purchases: ', FORMAT(COALESCE(cs.valid_purchase_total_amount, 0), 8), ' - Sold: ', FORMAT(COALESCE(cs.valid_sale_total_amount, 0) / 1.025, 8), ')*\\n',
                '6. GLOBAL_MINING_COSHARE: ', COALESCE(cs.valid_global_mining_coshare_count, 0), ' txn, total ', FORMAT(COALESCE(cs.valid_global_mining_coshare_total_amount, 0), 8), '\\n',
                '7. Final Adj. Balance: ', FORMAT(COALESCE(cs.final_adjusted_balance,0), 8), '\\n\\n',
                'Reconciliation:\\n',
                '- Dep + Profit + Comm - Valid W/D - Valid GMC = ', FORMAT(COALESCE(cs.deposit_total_amount,0)+COALESCE(cs.actual_nft_profit,0)+COALESCE(cs.commission_total_amount,0)-COALESCE(cs.valid_withdrawal_total_amount,0)-COALESCE(cs.valid_global_mining_coshare_total_amount,0), 8), '\\n',
                '- Final Adj. Balance + NFT Inventory = ', FORMAT(COALESCE(cs.final_adjusted_balance,0)+COALESCE(cs.unprocessed_valid_purchase_value,0), 8), '\\n\\n',
                '-----------------------------------------\\n',
                'CONCLUSION:\\n',
                '- Final Adj. Balance + NFT Inventory (1) = ', FORMAT(COALESCE(cs.final_adjusted_balance,0)+COALESCE(cs.unprocessed_valid_purchase_value,0), 8), '\\n',
                '- Invalid W/D (2) = ', FORMAT(COALESCE(cs.actual_withdrawal_total_amount,0)-COALESCE(cs.valid_withdrawal_total_amount,0), 8), '\\n',
                '- NFT Holdings (3) = ', FORMAT(COALESCE(cn.total_hold_nft_value,0) + COALESCE(pno.total_pending_nft_price_value,0), 8), '\\n\\n',
                '- Pending W/D (4) = ', FORMAT(COALESCE(pw.pending_withdrawal_amount,0), 8), '\\n',
                '- System Balance (5) = ', FORMAT(COALESCE(wi.usdtBalance,0), 8), '\\n\\n',
                '-----------------------------------------\\n',
                'DISCREPANCY:\\n',
                ' - Total (1) - (2) - (3) = ', FORMAT(COALESCE(cs.final_adjusted_balance,0)+COALESCE(cs.unprocessed_valid_purchase_value,0)-COALESCE(cs.actual_withdrawal_total_amount,0)+COALESCE(cs.valid_withdrawal_total_amount,0)-COALESCE(cn.total_hold_nft_value,0) - COALESCE(pno.total_pending_nft_price_value,0), 8), '\\n',
                ' - Total (4) + (5) = ', FORMAT(COALESCE(pw.pending_withdrawal_amount,0)+COALESCE(wi.usdtBalance,0), 8), '\\n\\n'
            ) AS full_financial_report,
            FORMAT(COALESCE(cs.final_adjusted_balance,0)+COALESCE(cs.unprocessed_valid_purchase_value,0)-COALESCE(cs.actual_withdrawal_total_amount,0)+COALESCE(cs.valid_withdrawal_total_amount,0)-COALESCE(cn.total_hold_nft_value,0) - COALESCE(pno.total_pending_nft_price_value,0)-COALESCE(pw.pending_withdrawal_amount,0), 8) AS discrepancy,
            FORMAT(COALESCE(wi.usdtBalance,0), 8) AS system_balance
        FROM
            SummaryCalculations cs -- Bảng tổng hợp chính
        LEFT JOIN UserInfo ui ON cs.userId = ui.id
        LEFT JOIN WalletInfo wi ON cs.userId = wi.userId
        LEFT JOIN CurrentNfts cn ON cs.userId = cn.owner_id
        LEFT JOIN PendingNftOrders pno ON cs.userId = pno.traderId
        LEFT JOIN PendingWithdrawals pw ON cs.userId = pw.userId`;

      // Thực thi truy vấn với tham số userId được truyền nhiều lần
      // Mỗi dấu ? trong câu lệnh SQL cần một tham số tương ứng
      const result = await this.dataSource.query(financialReportQuery, [
        userId, // BaseData
        userId, // OriginalCalculations
        userId, // UserInfo
        userId, // WalletInfo
        userId, // CurrentNfts
        userId, // PendingNftOrders
        userId  // PendingWithdrawals
      ]);

      if (!result || result.length === 0) {
        throw new NotFoundException({
          message: { en: 'Financial report not found' },
          code: 'FINANCIAL_REPORT_NOT_FOUND',
        });
      }

      return {
        fullFinancialReport: result[0].full_financial_report || '',
        discrepancy: result[0].discrepancy || '0.00000000',
        systemBalance: result[0].system_balance || '0.00000000'
      };
    } catch (error) {
      this.logger.error(`[getFinancialReport] Lỗi khi tạo báo cáo tài chính: ${error.message}`, error.stack);
      throw new InternalServerErrorException({
        message: { en: 'Failed to generate financial report' },
        code: 'FINANCIAL_REPORT_GENERATION_FAILED',
      });
    }
  }

  /**
   * Search user by wallet address or referral code
   * @param query Wallet address or referral code
   * @returns User if found, null otherwise
   */
  async searchUser(query: string): Promise<User | null> {
    this.logger.log(`[searchUser] Searching for user with query: ${query}`);

    try {
      // Try to find by wallet address first
      let user = await this.userRepository.findOne({
        where: { wallet: query }
      });

      // If not found by wallet, try by referral code
      if (!user) {
        user = await this.userRepository.findOne({
          where: { referralCode: query }
        });
      }

      // If user found, get token balances separately
      if (user) {
        this.logger.log(`[searchUser] User found: ${user.id} - ${user.username}`);

        // Get token balances for this user using TokenService
        const tokenBalances = await this.tokenService.getUserBalances(user.id);

        // Attach token balances to user object
        (user as any).tokenBalances = tokenBalances;
      } else {
        this.logger.log(`[searchUser] No user found for query: ${query}`);
      }

      return user;
    } catch (error) {
      this.logger.error(`[searchUser] Error searching user: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to search user');
    }
  }

  /**
   * Update user balance (admin only)
   * @param userId User ID
   * @param type Balance type (USDT or WM)
   * @param amount Amount to update
   * @param operation Operation type (add, subtract, set)
   * @param isCreateTransaction Whether to create transaction history record
   * @returns Updated user
   */
  async updateUserBalance(
    userId: string,
    type: 'USDT' | 'WM',
    amount: number,
    operation: 'add' | 'subtract' | 'set',
    isCreateTransaction: boolean = true
  ): Promise<User> {
    this.logger.log(`[updateUserBalance] Updating ${type} balance for user ${userId}: ${operation} ${amount}`);

    try {
      // Find user
      const user = await this.findOne(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Find token
      const token = await this.tokenService.findBySymbol(type);
      if (!token) {
        throw new NotFoundException(`Token ${type} not found`);
      }

      // Get current token balance
      let currentTokenBalance: TokenBalance;
      try {
        currentTokenBalance = await this.tokenService.findTokenByUserIdAndSymbol(userId, type);
      } catch (error) {
        throw new NotFoundException(`${type} balance not found for user`);
      }

      const currentBalance = Number(currentTokenBalance.availableBalance);

      // Calculate new balance
      let newBalance: number;
      switch (operation) {
        case 'add':
          newBalance = currentBalance + amount;
          break;
        case 'subtract':
          newBalance = Math.max(0, currentBalance - amount); // Không cho phép âm
          break;
        case 'set':
          newBalance = amount;
          break;
        default:
          throw new BadRequestException('Invalid operation');
      }

      // Calculate the difference for TokenService update
      const difference = newBalance - currentBalance;
      const updateOperation = difference >= 0 ? 'add' : 'subtract';
      const updateAmount = Math.abs(difference);

      // Update token balance using TokenService
      if (updateAmount > 0) {
        await this.tokenService.updateTokenBalance(userId, token.id, updateAmount, updateOperation);
      }

      // Find wallet and update wallet balance
      const wallet = await this.dataSource.getRepository(Wallet).findOne({
        where: { userId }
      });

      if (wallet) {
        if (type === 'USDT') {
          wallet.usdtBalance = newBalance;
        } else if (type === 'WM') {
          wallet.wmBalance = newBalance;
        }
        wallet.updatedAt = new Date();
        await this.dataSource.getRepository(Wallet).save(wallet);

        // Create transaction record if requested
        if (isCreateTransaction) {
          const transaction = this.dataSource.getRepository(Transaction).create({
            walletId: wallet.id,
            userId: user.id,
            tokenId: token.id,
            type: TransactionType.TASK_REWARD,
            amount: operation === 'subtract' ? -amount : amount,
            status: TransactionStatus.COMPLETED,
            reference: `admin-${operation}`,
            note: `Admin ${operation} ${amount} ${type}`,
            transactionAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          });

          await this.dataSource.getRepository(Transaction).save(transaction);
        }
      }

      this.logger.log(`[updateUserBalance] Successfully updated ${type} balance for user ${userId}: ${currentBalance} -> ${newBalance}`);

      // Return updated user with token balances
      const updatedUser = await this.userRepository.findOne({
        where: { id: userId }
      });

      // Get token balances using TokenService
      const tokenBalances = await this.tokenService.getUserBalances(userId);

      // Attach token balances to user object
      (updatedUser as any).tokenBalances = tokenBalances;

      return updatedUser;

    } catch (error) {
      this.logger.error(`[updateUserBalance] Error updating balance: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException('Failed to update user balance');
    }
  }
}