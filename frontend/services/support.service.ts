import { useApi } from '@/hooks/useApi';

export interface Ticket {
  id: string;
  ticketCode: string;
  userId: string;
  userReferralCode: string;
  content: string;
  txHash?: string;
  status: 'pending' | 'processing' | 'resolved';
  response?: string;
  walletAddress: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTicketRequest {
  content: string;
  txHash?: string;
  walletAddress: string;
}

export const useSupportService = () => {
  const { fetchApi } = useApi();

  /**
   * Create a new support ticket
   * @param data Ticket data
   * @returns Created ticket
   */
  const createTicket = async (data: CreateTicketRequest) => {
    return await fetchApi<Ticket>('/support/tickets', {
      method: 'POST',
      body: data,
      requireAuth: true,
    });
  };

  /**
   * Get all tickets for the current user
   * @returns List of tickets
   */
  const getUserTickets = async () => {
    return await fetchApi<Ticket[]>('/support/tickets', {
      method: 'GET',
      requireAuth: true,
    });
  };

  /**
   * Get a ticket by ID
   * @param id Ticket ID
   * @returns Ticket
   */
  const getTicketById = async (id: string) => {
    return await fetchApi<Ticket>(`/support/tickets/${id}`, {
      method: 'GET',
      requireAuth: true,
    });
  };

  return {
    createTicket,
    getUserTickets,
    getTicketById,
  };
};
