'use client';

import React from 'react';
import { Listbox, ListboxItem, cn, Select, SelectItem } from '@heroui/react';
import { Icon } from '@iconify/react';
import { ThemeSwitch } from '@/components/theme-switch';
import { useTranslation } from '@/hooks/useTranslation';

const IconWrapper = ({ children, className }: { children: React.ReactNode; className: string }) => (
  <div className={cn(className, "flex items-center rounded-small justify-center w-7 h-7")}>
    {children}
  </div>
);

type Language = 'en' | 'vi' | 'kr' | 'zh' | 'ja' | 'th' | 'ru';
export default function MoreToolsSection() {
  const { t, language, setLanguage } = useTranslation();
  const languages = [
    { key: 'en', label: 'English' },
    { key: 'vi', label: 'Tiếng Việt' },
    { key: 'kr', label: '한국어' },
    { key: 'zh', label: '中文' },
    { key: 'ja', label: '日本語' },
    { key: 'th', label: 'ภาษาไทย' },
    { key: 'ru', label: 'Русский' },
  ];

  return (
    <div className="flex flex-col gap-3">
      <p className="font-semibold text-lg">{t('account.more_tools')}</p>
      <Listbox
        aria-label="More Tools Menu"
        className="p-0 gap-0 divide-y divide-default-300/50 dark:divide-default-100/80 bg-content1 overflow-visible shadow-small rounded-medium"
        itemClasses={{
          base: "px-3 first:rounded-t-medium last:rounded-b-medium rounded-none gap-3 h-12 data-[hover=true]:bg-default-100/80",
          title: "text-left",
        }}
        onAction={(key) => {}}
      >
        <ListboxItem
          href="/edit-profile"
          key="edit-my-profile"
          endContent={<Icon icon="solar:arrow-right-linear" className="text-gray-500" />}
          startContent={
            <IconWrapper className="bg-gradient-to-r from-blue-500 to-green-500">
              <Icon icon="solar:user-linear" className="text-white" />
            </IconWrapper>
          }
        >
          {t('account.edit_profile')}
        </ListboxItem>
        <ListboxItem
          href="/wallet"
          key="buy-sell-analytics"
          endContent={<Icon icon="solar:arrow-right-linear" className="text-gray-500" />}
          startContent={
            <IconWrapper className="bg-gradient-to-r from-blue-500 to-green-500">
              <Icon icon="solar:chart-linear" className="text-white" />
            </IconWrapper>
          }
        >
          {t('account.buy_sell_analytics')}
        </ListboxItem>
        <ListboxItem
          href="/credit-histories"
          key="account-credit-history"
          endContent={<Icon icon="solar:arrow-right-linear" className="text-gray-500" />}
          startContent={
            <IconWrapper className="bg-gradient-to-r from-blue-500 to-green-500">
              <Icon icon="solar:heart-linear" className="text-white" />
            </IconWrapper>
          }
        >
          {t('account.credit_history')}
        </ListboxItem>
        <ListboxItem
          href="/flash-buy"
          key="flash-buy-alarms-listing"
          endContent={<Icon icon="solar:arrow-right-linear" className="text-gray-500" />}
          startContent={
            <IconWrapper className="bg-gradient-to-r from-blue-500 to-green-500">
              <Icon icon="solar:alarm-linear" className="text-white" />
            </IconWrapper>
          }
        >
          {t('account.flash_buy_alarms')}
        </ListboxItem>
        <ListboxItem
          href="/global-mining-co-share"
          key="global-mining-co-share"
          endContent={<Icon icon="solar:arrow-right-linear" className="text-gray-500" />}
          startContent={
            <IconWrapper className="bg-gradient-to-r from-blue-500 to-green-500">
              <Icon icon="solar:course-up-linear" className="text-white" />
            </IconWrapper>
          }
        >
          {t('account.global_mining')}
        </ListboxItem>
        <ListboxItem
          href="/support"
          key="help-center"
          endContent={<Icon icon="solar:arrow-right-linear" className="text-gray-500" />}
          startContent={
            <IconWrapper className="bg-gradient-to-r from-blue-500 to-green-500">
              <Icon icon="solar:chat-round-linear" className="text-white" />
            </IconWrapper>
          }
        >
          {t('account.help_center')}
        </ListboxItem>
        <ListboxItem
          key="language"
          endContent={
            <Select
              selectedKeys={[language]}
              className="w-36"
              size="sm"
              onChange={(e) => {
                if (e.target.value === 'en' || e.target.value === 'vi' ||
                    e.target.value === 'kr' || e.target.value === 'zh' ||
                    e.target.value === 'ja' || e.target.value === 'th' ||
                    e.target.value === 'ru') {
                  setLanguage(e.target.value as Language);
                }
              }}
            >
              {languages.map((lang) => (
                <SelectItem key={lang.key}>
                  {lang.label}
                </SelectItem>
              ))}
            </Select>
          }
          startContent={
            <IconWrapper className="bg-gradient-to-r from-blue-500 to-green-500">
              <Icon icon="solar:flag-linear" className="text-white" />
            </IconWrapper>
          }
        >
          {t('common.language')}
        </ListboxItem>
        <ListboxItem
          key="theme"
          endContent={<ThemeSwitch />}
          startContent={
            <IconWrapper className="bg-gradient-to-r from-purple-500 to-pink-500">
              <Icon icon="solar:moon-linear" className="text-white" />
            </IconWrapper>
          }
        >
          {t('common.theme')}
        </ListboxItem>
      </Listbox>
    </div>
  );
}