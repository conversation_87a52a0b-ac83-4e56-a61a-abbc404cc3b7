'use client';

import React from 'react';
import { Card, CardBody } from '@heroui/react';
import { useUserStore } from '@/store/userStore';
import { UserRole } from '@/types/models';

export default function DebugUserInfo() {
  const { user } = useUserStore();

  if (!user) {
    return (
      <Card className="w-full mb-4 border-2 border-red-500">
        <CardBody className="p-4">
          <h3 className="text-lg font-semibold text-red-600 mb-2">Debug: User Info</h3>
          <p className="text-red-500">No user data found</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="w-full mb-4 border-2 border-blue-500">
      <CardBody className="p-4">
        <h3 className="text-lg font-semibold text-blue-600 mb-2">Debug: User Info</h3>
        <div className="space-y-2 text-sm">
          <p><strong>ID:</strong> {user.id}</p>
          <p><strong>Username:</strong> {user.username}</p>
          <p><strong>Wallet:</strong> {user.wallet}</p>
          <p><strong>Role:</strong> <span className="font-mono bg-gray-100 px-2 py-1 rounded">{user.role}</span></p>
          <p><strong>Is Admin:</strong> <span className={`font-mono px-2 py-1 rounded ${user.role === UserRole.ADMIN ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {user.role === UserRole.ADMIN ? 'YES' : 'NO'}
          </span></p>
          <p><strong>Referral Code:</strong> {user.referralCode}</p>
        </div>
      </CardBody>
    </Card>
  );
}
