'use client';

import React, { useState } from 'react';
import {
  Card,
  CardBody,
  Input,
  But<PERSON>,
  Spinner
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';
import UserDetailsCard from './components/user-details-card';
import UpdateBalanceModal from './components/update-balance-modal';

export default function AdminUserManagementPage() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchedUser, setSearchedUser] = useState<User | null>(null);
  const [searchError, setSearchError] = useState<string>('');
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const [updateType, setUpdateType] = useState<'USDT' | 'WM'>('USDT');

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchError('Vui lòng nhập địa chỉ ví hoặc mã giới thiệu');
      return;
    }

    setIsSearching(true);
    setSearchError('');
    setSearchedUser(null);

    try {
      const response = await fetch(`/api/v1/users/admin/search?query=${encodeURIComponent(searchQuery.trim())}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        setSearchedUser(result.data);
      } else if (response.status === 404) {
        setSearchError('Không tìm thấy người dùng với thông tin này');
      } else {
        setSearchError('Có lỗi xảy ra khi tìm kiếm');
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchError('Có lỗi xảy ra khi tìm kiếm');
    } finally {
      setIsSearching(false);
    }
  };

  const handleUpdateBalance = (type: 'USDT' | 'WM') => {
    setUpdateType(type);
    setIsUpdateModalOpen(true);
  };

  const handleBalanceUpdated = (updatedUser: User) => {
    setSearchedUser(updatedUser);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        {/* Header */}
        <div className="flex items-center gap-3 mb-2">
          <Icon icon="solar:users-group-two-rounded-bold-duotone" className="text-blue-500" width={24} />
          <h2 className="text-xl font-semibold">{t('admin.user_management')}</h2>
        </div>

        {/* Search Section */}
        <Card className="w-full">
          <CardBody className="p-4">
            <div className="flex flex-col gap-3">
              <div className="text-sm font-medium text-gray-700">
                {t('admin.search_user')}
              </div>
              
              <div className="flex gap-2">
                <Input
                  placeholder={t('admin.search_user_placeholder')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={handleKeyPress}
                  startContent={<Icon icon="solar:magnifer-linear" width={20} />}
                  classNames={{
                    input: "text-sm",
                  }}
                />
                <Button
                  color="primary"
                  onPress={handleSearch}
                  isLoading={isSearching}
                  isDisabled={!searchQuery.trim()}
                >
                  {isSearching ? <Spinner size="sm" /> : t('admin.search')}
                </Button>
              </div>

              {searchError && (
                <div className="text-sm text-red-500 flex items-center gap-2">
                  <Icon icon="solar:danger-circle-linear" width={16} />
                  {searchError}
                </div>
              )}
            </div>
          </CardBody>
        </Card>

        {/* Search Results */}
        {searchedUser && (
          <UserDetailsCard 
            user={searchedUser} 
            onUpdateBalance={handleUpdateBalance}
          />
        )}

        {/* Update Balance Modal */}
        <UpdateBalanceModal
          isOpen={isUpdateModalOpen}
          onClose={() => setIsUpdateModalOpen(false)}
          user={searchedUser}
          type={updateType}
          onBalanceUpdated={handleBalanceUpdated}
        />
      </div>
    </div>
  );
}
