'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  Button,
  Input,
  Checkbox,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface AdminSetBalanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  type: 'USDT' | 'WM';
  onBalanceUpdated: (updatedUser: User) => void;
}

export default function AdminSetBalanceModal({
  isOpen,
  onClose,
  user,
  type,
  onBalanceUpdated
}: AdminSetBalanceModalProps) {
  const { t } = useTranslation();
  const [amount, setAmount] = useState('');
  const [isCreateTransaction, setIsCreateTransaction] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setAmount('');
      setIsCreateTransaction(true);
    }
  }, [isOpen]);

  // Get current balance
  const getCurrentBalance = (): number => {
    if (!user) return 0;
    const tokenBalance = user.tokenBalances?.find(tb => tb.token.symbol === type);
    return tokenBalance ? Number(tokenBalance.availableBalance) : 0;
  };

  const handleSubmit = async () => {
    if (!user || !amount || parseFloat(amount) < 0) {
      addToast({
        title: t('errors.error'),
        description: t('admin.enter_valid_amount_set'),
        color: 'danger',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/v1/users/admin/${user.id}/balance`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          amount: parseFloat(amount),
          operation: 'set',
          isCreateTransaction
        }),
      });

      if (response.ok) {
        const result = await response.json();
        onBalanceUpdated(result.data);
        addToast({
          title: t('common.success'),
          description: t('admin.balance_set_successfully', { type, amount }),
          color: 'success',
        });
        onClose();
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message || t('admin.balance_update_error'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Set balance error:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.balance_update_error'),
        color: 'danger',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="sm">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon
              icon={type === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'}
              width={24}
              className={type === 'USDT' ? 'text-green-500' : 'text-blue-500'}
            />
            <h3 className="text-lg font-semibold">
              {t('admin.set_balance')} {type}
            </h3>
          </div>
          <p className="text-sm text-gray-500">
            {user.name || user.username || `${user.wallet.substring(0, 6)}...${user.wallet.substring(user.wallet.length - 6)}`}
          </p>
        </ModalHeader>

        <ModalBody>
          <div className="flex flex-col gap-4">
            {/* Current Balance Info */}
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">{t('admin.current_balance')}</span>
              <span className={`text-sm font-bold ${type === 'USDT' ? 'text-green-600' : 'text-blue-600'}`}>
                {getCurrentBalance().toLocaleString()} {type}
              </span>
            </div>

            {/* New Balance Amount */}
            <Input
              type="number"
              label={`${t('admin.new_balance')} (${type})`}
              placeholder={t('admin.enter_new_balance_placeholder', { type })}
              value={amount}
              onValueChange={setAmount}
              startContent={
                <Icon
                  icon={type === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'}
                  width={16}
                />
              }
            />

            {/* Transaction History Checkbox */}
            <Checkbox
              isSelected={isCreateTransaction}
              onValueChange={setIsCreateTransaction}
            >
              {t('admin.create_transaction_history')}
            </Checkbox>

            {/* Preview */}
            {amount && parseFloat(amount) >= 0 && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-sm text-blue-600 mb-1">
                  {t('admin.balance_change_preview')}
                </div>
                <div className="text-lg font-bold text-blue-700">
                  {parseFloat(amount).toLocaleString()} {type}
                </div>
                <div className="text-xs text-blue-500 mt-1">
                  {parseFloat(amount) > getCurrentBalance() 
                    ? `+${(parseFloat(amount) - getCurrentBalance()).toLocaleString()} ${type}`
                    : parseFloat(amount) < getCurrentBalance()
                    ? `-${(getCurrentBalance() - parseFloat(amount)).toLocaleString()} ${type}`
                    : t('admin.no_change')
                  }
                </div>
              </div>
            )}

            {/* Warning */}
            <div className="flex flex-col gap-2 bg-warning-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-warning-700">
                {t('admin.set_balance_warning')}
              </p>
            </div>
          </div>
        </ModalBody>

        <ModalFooter className="flex flex-col gap-2">
          <Button
            color="primary"
            className="w-full"
            onPress={handleSubmit}
            isLoading={isSubmitting}
            isDisabled={!amount || parseFloat(amount) < 0}
          >
            {t('admin.set_balance')}
          </Button>
          <Button
            color="danger"
            variant="light"
            className="w-full"
            onPress={onClose}
            isDisabled={isSubmitting}
          >
            {t('common.cancel')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
