'use client';

import React from 'react';
import { 
  Card, 
  CardBody, 
  Button, 
  Chip,
  Avatar,
  Divider
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface UserDetailsCardProps {
  user: User;
  onUpdateBalance: (type: 'USDT' | 'WM') => void;
}

export default function UserDetailsCard({ user, onUpdateBalance }: UserDetailsCardProps) {
  const { t } = useTranslation();

  // Shorten wallet address for display
  const shortenAddress = (address: string): string => {
    if (!address || address.length < 10) return address;
    return `${address.substring(0, 6)}...${address.substring(address.length - 6)}`;
  };

  // Handle copy wallet address
  const handleCopyWallet = async (walletAddress: string) => {
    try {
      await navigator.clipboard.writeText(walletAddress);
      // You can add toast notification here if needed
    } catch (error) {
      console.error('Failed to copy wallet address:', error);
    }
  };

  // Get user status color
  const getUserStatusColor = () => {
    if (user.isKycCompleted) return 'success';
    if (user.firstDepositTime) return 'warning';
    return 'default';
  };

  // Get user status text
  const getUserStatusText = () => {
    if (user.isKycCompleted) return 'KYC Verified';
    if (user.firstDepositTime) return 'Active';
    return 'New User';
  };

  // Get balance from tokenBalances
  const getTokenBalance = (symbol: string): number => {
    const tokenBalance = user.tokenBalances?.find(tb => tb.token.symbol === symbol);
    return tokenBalance ? Number(tokenBalance.availableBalance) : 0;
  };

  const usdtBalance = getTokenBalance('USDT');
  const wmBalance = getTokenBalance('WM');

  return (
    <div className="flex flex-col gap-4">
      {/* User Info Card */}
      <Card className="w-full">
        <CardBody className="p-4">
          <div className="flex items-center gap-3 mb-4">
            <Avatar
              size="lg"
              name={user.name || user.username || shortenAddress(user.wallet)}
              className="flex-shrink-0"
            />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="text-lg font-semibold">
                  {user.name || user.username || shortenAddress(user.wallet)}
                </h3>
                <Chip color={getUserStatusColor()} size="sm" variant="flat">
                  {getUserStatusText()}
                </Chip>
              </div>
              <div className="text-sm text-gray-500">
                ID: {user.id}
              </div>
            </div>
          </div>

          <Divider className="my-3" />

          {/* Basic Information */}
          <div className="space-y-3">
            <div className="text-sm font-medium text-gray-700 mb-2">
              {t('admin.basic_info')}
            </div>

            <div className="grid grid-cols-1 gap-3">
              <div>
                <span className="text-sm font-medium">{t('admin.wallet_address')}:</span>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                    {shortenAddress(user.wallet)}
                  </span>
                  <Button
                    isIconOnly
                    variant="light"
                    size="sm"
                    onPress={() => handleCopyWallet(user.wallet)}
                  >
                    <Icon icon="solar:copy-linear" width={16} />
                  </Button>
                </div>
              </div>

              <div>
                <span className="text-sm font-medium">{t('admin.referral_code')}:</span>
                <span className="text-sm ml-2 font-mono bg-gray-100 px-2 py-1 rounded">
                  {user.referralCode}
                </span>
              </div>

              {user.email && (
                <div>
                  <span className="text-sm font-medium">Email:</span>
                  <span className="text-sm ml-2">{user.email}</span>
                </div>
              )}

              {user.phone && (
                <div>
                  <span className="text-sm font-medium">Phone:</span>
                  <span className="text-sm ml-2">{user.phone}</span>
                </div>
              )}

              <div>
                <span className="text-sm font-medium">{t('admin.role')}:</span>
                <Chip color={user.role === 'ADMIN' ? 'danger' : 'primary'} size="sm" variant="flat" className="ml-2">
                  {user.role}
                </Chip>
              </div>

              <div>
                <span className="text-sm font-medium">{t('admin.rank')}:</span>
                <Chip color="secondary" size="sm" variant="flat" className="ml-2">
                  {user.rank}
                </Chip>
              </div>

              <div>
                <span className="text-sm font-medium">{t('admin.joined_date')}:</span>
                <span className="text-sm ml-2">
                  {new Date(user.createdAt).toLocaleString('vi-VN')}
                </span>
              </div>

              {user.firstDepositTime && (
                <div>
                  <span className="text-sm font-medium">{t('admin.first_deposit')}:</span>
                  <span className="text-sm ml-2">
                    {new Date(user.firstDepositTime).toLocaleString('vi-VN')}
                  </span>
                </div>
              )}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Account Balance Card */}
      <Card className="w-full">
        <CardBody className="p-4">
          <div className="text-sm font-medium text-gray-700 mb-3">
            {t('admin.account_balance')}
          </div>

          <div className="space-y-3">
            {/* USDT Balance */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <Icon icon="solar:dollar-minimalistic-linear" width={16} className="text-green-600" />
                </div>
                <div>
                  <div className="text-sm font-medium">USDT Balance</div>
                  <div className="text-lg font-bold text-green-600">
                    {usdtBalance.toLocaleString()} USDT
                  </div>
                </div>
              </div>
              <Button
                size="sm"
                color="primary"
                variant="flat"
                onPress={() => onUpdateBalance('USDT')}
                startContent={<Icon icon="solar:pen-linear" width={16} />}
              >
                {t('admin.update')}
              </Button>
            </div>

            {/* WM Balance */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Icon icon="solar:medal-ribbon-linear" width={16} className="text-blue-600" />
                </div>
                <div>
                  <div className="text-sm font-medium">WM Balance</div>
                  <div className="text-lg font-bold text-blue-600">
                    {wmBalance.toLocaleString()} WM
                  </div>
                </div>
              </div>
              <Button
                size="sm"
                color="primary"
                variant="flat"
                onPress={() => onUpdateBalance('WM')}
                startContent={<Icon icon="solar:pen-linear" width={16} />}
              >
                {t('admin.update')}
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
