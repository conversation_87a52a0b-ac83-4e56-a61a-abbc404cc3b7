'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>ooter,
  Button,
  Input,
  Select,
  SelectItem,
  Divider,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';

interface UpdateBalanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  type: 'USDT' | 'WM';
  onBalanceUpdated: (updatedUser: User) => void;
}

export default function UpdateBalanceModal({
  isOpen,
  onClose,
  user,
  type,
  onBalanceUpdated
}: UpdateBalanceModalProps) {
  const { t } = useTranslation();
  const [amount, setAmount] = useState('');
  const [operation, setOperation] = useState<'add' | 'subtract' | 'set'>('add');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setAmount('');
      setOperation('add');
    }
  }, [isOpen]);

  // Get current balance
  const getCurrentBalance = (): number => {
    if (!user) return 0;
    const tokenBalance = user.tokenBalances?.find(tb => tb.token.symbol === type);
    return tokenBalance ? Number(tokenBalance.availableBalance) : 0;
  };

  // Calculate new balance preview
  const getNewBalance = (): number => {
    const currentBalance = getCurrentBalance();
    const amountNum = parseFloat(amount) || 0;

    switch (operation) {
      case 'add':
        return currentBalance + amountNum;
      case 'subtract':
        return Math.max(0, currentBalance - amountNum);
      case 'set':
        return amountNum;
      default:
        return currentBalance;
    }
  };

  const handleSubmit = async () => {
    if (!user || !amount || parseFloat(amount) < 0) {
      addToast({
        title: t('errors.error'),
        description: 'Vui lòng nhập số tiền hợp lệ',
        color: 'danger',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/v1/users/admin/${user.id}/balance`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          amount: parseFloat(amount),
          operation
        }),
      });

      if (response.ok) {
        const result = await response.json();
        onBalanceUpdated(result.data);
        addToast({
          title: t('common.success'),
          description: `Đã cập nhật ${type} balance thành công`,
          color: 'success',
        });
        onClose();
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message || 'Có lỗi xảy ra khi cập nhật balance',
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Update balance error:', error);
      addToast({
        title: t('errors.error'),
        description: 'Có lỗi xảy ra khi cập nhật balance',
        color: 'danger',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon 
              icon={type === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'} 
              width={24} 
              className={type === 'USDT' ? 'text-green-500' : 'text-blue-500'} 
            />
            <h3 className="text-lg font-semibold">
              {t('admin.update_balance')} - {type}
            </h3>
          </div>
          <p className="text-sm text-gray-500">
            {user.name || user.username || `${user.wallet.substring(0, 6)}...${user.wallet.substring(user.wallet.length - 6)}`}
          </p>
        </ModalHeader>

        <ModalBody>
          <div className="flex flex-col gap-4">
            {/* Current Balance */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">
                {t('admin.current_balance')}
              </div>
              <div className={`text-lg font-bold ${type === 'USDT' ? 'text-green-600' : 'text-blue-600'}`}>
                {getCurrentBalance().toLocaleString()} {type}
              </div>
            </div>

            {/* Operation Type */}
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin.operation_type')}
              </label>
              <Select
                selectedKeys={[operation]}
                onSelectionChange={(keys) => setOperation(Array.from(keys)[0] as 'add' | 'subtract' | 'set')}
                classNames={{
                  trigger: "h-10",
                }}
              >
                <SelectItem key="add" startContent={<Icon icon="solar:add-circle-linear" width={16} />}>
                  {t('admin.add')} - Cộng thêm
                </SelectItem>
                <SelectItem key="subtract" startContent={<Icon icon="solar:minus-circle-linear" width={16} />}>
                  {t('admin.subtract')} - Trừ bớt
                </SelectItem>
                <SelectItem key="set" startContent={<Icon icon="solar:settings-linear" width={16} />}>
                  {t('admin.set')} - Đặt giá trị
                </SelectItem>
              </Select>
            </div>

            {/* Amount */}
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin.amount')} ({type})
              </label>
              <Input
                type="number"
                placeholder={`Nhập số lượng ${type}`}
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                min="0"
                step="0.01"
                startContent={
                  <Icon 
                    icon={type === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'} 
                    width={16} 
                  />
                }
              />
            </div>

            {/* Preview */}
            {amount && (
              <>
                <Divider />
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm text-blue-600 mb-1">
                    {t('admin.new_balance_preview')}
                  </div>
                  <div className="text-lg font-bold text-blue-700">
                    {getNewBalance().toLocaleString()} {type}
                  </div>
                  <div className="text-xs text-blue-500 mt-1">
                    {operation === 'add' && `+${parseFloat(amount).toLocaleString()} ${type}`}
                    {operation === 'subtract' && `-${parseFloat(amount).toLocaleString()} ${type}`}
                    {operation === 'set' && `Set to ${parseFloat(amount).toLocaleString()} ${type}`}
                  </div>
                </div>
              </>
            )}
          </div>
        </ModalBody>

        <ModalFooter>
          <Button
            color="danger"
            variant="light"
            onPress={onClose}
            isDisabled={isSubmitting}
          >
            {t('common.cancel')}
          </Button>
          <Button
            color="primary"
            onPress={handleSubmit}
            isLoading={isSubmitting}
            isDisabled={!amount || parseFloat(amount) < 0}
          >
            {t('admin.update_balance')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
