'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON>ton,
  Select,
  SelectItem,
  Divider,
  addToast
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useTranslation } from '@/hooks/useTranslation';
import { User } from '@/types/models';
import MobileSafeInput from '@/components/mobile-safe-input';

interface UpdateBalanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  type: 'USDT' | 'WM';
  onBalanceUpdated: (updatedUser: User) => void;
}

export default function UpdateBalanceModal({
  isOpen,
  onClose,
  user,
  type,
  onBalanceUpdated
}: UpdateBalanceModalProps) {
  const { t } = useTranslation();
  const [amount, setAmount] = useState('');
  const [operation, setOperation] = useState<'add' | 'subtract' | 'set'>('add');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setAmount('');
      setOperation('add');
    }
  }, [isOpen]);

  // Get current balance
  const getCurrentBalance = (): number => {
    if (!user) return 0;
    const tokenBalance = user.tokenBalances?.find(tb => tb.token.symbol === type);
    return tokenBalance ? Number(tokenBalance.availableBalance) : 0;
  };

  // Calculate new balance preview
  const getNewBalance = (): number => {
    const currentBalance = getCurrentBalance();
    const amountNum = parseFloat(amount) || 0;

    switch (operation) {
      case 'add':
        return currentBalance + amountNum;
      case 'subtract':
        return Math.max(0, currentBalance - amountNum);
      case 'set':
        return amountNum;
      default:
        return currentBalance;
    }
  };

  const handleSubmit = async () => {
    if (!user || !amount || parseFloat(amount) <= 0) {
      addToast({
        title: t('errors.error'),
        description: 'Vui lòng nhập số tiền hợp lệ (lớn hơn 0)',
        color: 'danger',
      });
      return;
    }

    if (operation === 'subtract' && parseFloat(amount) > getCurrentBalance()) {
      addToast({
        title: t('errors.error'),
        description: 'Số tiền trừ không thể lớn hơn số dư hiện tại',
        color: 'danger',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/v1/users/admin/${user.id}/balance`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          amount: parseFloat(amount),
          operation
        }),
      });

      if (response.ok) {
        const result = await response.json();
        onBalanceUpdated(result.data);
        addToast({
          title: t('common.success'),
          description: `Đã cập nhật ${type} balance thành công`,
          color: 'success',
        });
        onClose();
      } else {
        const error = await response.json();
        addToast({
          title: t('errors.error'),
          description: error.message || 'Có lỗi xảy ra khi cập nhật balance',
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Update balance error:', error);
      addToast({
        title: t('errors.error'),
        description: 'Có lỗi xảy ra khi cập nhật balance',
        color: 'danger',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" scrollBehavior="inside">
      <ModalContent className="max-h-[90vh]">
        <ModalHeader className="flex flex-col gap-1 pb-2">
          <div className="flex items-center gap-2">
            <Icon
              icon={type === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'}
              width={24}
              className={type === 'USDT' ? 'text-green-500' : 'text-blue-500'}
            />
            <h3 className="text-lg font-semibold">
              {t('admin.update_balance')} - {type}
            </h3>
          </div>
          <p className="text-sm text-gray-500">
            {user.name || user.username || `${user.wallet.substring(0, 6)}...${user.wallet.substring(user.wallet.length - 6)}`}
          </p>
        </ModalHeader>

        <ModalBody className="py-4">
          <div className="flex flex-col gap-6 mobile-input-fix">
            {/* Current Balance */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">
                {t('admin.current_balance')}
              </div>
              <div className={`text-lg font-bold ${type === 'USDT' ? 'text-green-600' : 'text-blue-600'}`}>
                {getCurrentBalance().toLocaleString()} {type}
              </div>
            </div>

            {/* Operation Type */}
            <div>
              <Select
                label={t('admin.operation_type')}
                labelPlacement="outside"
                placeholder="Chọn loại thao tác"
                selectedKeys={[operation]}
                defaultSelectedKeys={['add']}
                onSelectionChange={(keys) => setOperation(Array.from(keys)[0] as 'add' | 'subtract' | 'set')}
                classNames={{
                  trigger: "h-12",
                  label: "text-sm font-medium text-gray-700",
                }}
              >
                <SelectItem key="add" startContent={<Icon icon="solar:add-circle-linear" width={16} />}>
                  {t('admin.add')} - Cộng thêm
                </SelectItem>
                <SelectItem key="subtract" startContent={<Icon icon="solar:minus-circle-linear" width={16} />}>
                  {t('admin.subtract')} - Trừ bớt
                </SelectItem>
                <SelectItem key="set" startContent={<Icon icon="solar:settings-linear" width={16} />}>
                  {t('admin.set')} - Đặt giá trị
                </SelectItem>
              </Select>
            </div>

            {/* Amount */}
            <div>
              <MobileSafeInput
                type="number"
                label={`${t('admin.amount')} (${type})`}
                labelPlacement="outside"
                placeholder={`Nhập số lượng ${type}`}
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                min="0"
                step="0.01"
                inputMode="decimal"
                startContent={
                  <Icon
                    icon={type === 'USDT' ? 'solar:dollar-minimalistic-linear' : 'solar:medal-ribbon-linear'}
                    width={16}
                  />
                }
                classNames={{
                  label: "text-sm font-medium text-gray-700",
                  inputWrapper: "h-12",
                }}
              />
            </div>

            {/* Preview */}
            {amount && parseFloat(amount) > 0 && (
              <>
                <Divider className="my-2" />
                <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Icon icon="solar:eye-linear" width={16} className="text-blue-600" />
                    <span className="text-sm font-medium text-blue-600">
                      {t('admin.new_balance_preview')}
                    </span>
                  </div>
                  <div className="text-xl font-bold text-blue-700 mb-1">
                    {getNewBalance().toLocaleString()} {type}
                  </div>
                  <div className="text-sm text-blue-600">
                    {operation === 'add' && (
                      <span className="flex items-center gap-1">
                        <Icon icon="solar:add-circle-linear" width={14} />
                        +{parseFloat(amount).toLocaleString()} {type}
                      </span>
                    )}
                    {operation === 'subtract' && (
                      <span className="flex items-center gap-1">
                        <Icon icon="solar:minus-circle-linear" width={14} />
                        -{parseFloat(amount).toLocaleString()} {type}
                      </span>
                    )}
                    {operation === 'set' && (
                      <span className="flex items-center gap-1">
                        <Icon icon="solar:settings-linear" width={14} />
                        Đặt thành {parseFloat(amount).toLocaleString()} {type}
                      </span>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        </ModalBody>

        <ModalFooter className="pt-4">
          <div className="flex gap-3 w-full">
            <Button
              color="danger"
              variant="light"
              onPress={onClose}
              isDisabled={isSubmitting}
              className="flex-1 h-12"
            >
              {t('common.cancel')}
            </Button>
            <Button
              color="primary"
              onPress={handleSubmit}
              isLoading={isSubmitting}
              isDisabled={!amount || parseFloat(amount) <= 0 || (operation === 'subtract' && parseFloat(amount) > getCurrentBalance())}
              className="flex-1 h-12"
            >
              {t('admin.update_balance')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
