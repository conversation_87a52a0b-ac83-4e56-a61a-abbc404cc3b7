'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON>ooter,
  Button,
  Textarea,
  Select,
  SelectItem,
  Chip,
  addToast,
  Divider
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { Ticket, useSupportService, UpdateTicketRequest } from '@/services/support.service';
import { useTranslation } from '@/hooks/useTranslation';

interface TicketResponseModalProps {
  isOpen: boolean;
  onClose: () => void;
  ticket: Ticket | null;
  onTicketUpdated: (updatedTicket: Ticket) => void;
}

export default function TicketResponseModal({
  isOpen,
  onClose,
  ticket,
  onTicketUpdated
}: TicketResponseModalProps) {
  const supportService = useSupportService();
  const { t } = useTranslation();
  const [response, setResponse] = useState('');
  const [status, setStatus] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (ticket) {
      setResponse(ticket.response || '');
      setStatus(ticket.status);
    }
  }, [ticket]);

  const handleSubmit = async () => {
    if (!ticket) return;

    if (!response.trim()) {
      addToast({
        title: t('errors.error'),
        description: t('support.fill_required_fields'),
        color: 'danger',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const updateData: UpdateTicketRequest = {
        response: response.trim(),
        status: status as 'pending' | 'processing' | 'resolved'
      };

      const result = await supportService.updateTicket(ticket.id, updateData);

      if (result.success && result.data) {
        addToast({
          title: t('common.success'),
          description: t('admin.ticket_updated'),
          color: 'success',
        });

        onTicketUpdated(result.data);
        onClose();
      } else {
        addToast({
          title: t('errors.error'),
          description: typeof result.message === 'string' ? result.message : t('admin.update_failed'),
          color: 'danger',
        });
      }
    } catch (error) {
      console.error('Error updating ticket:', error);
      addToast({
        title: t('errors.error'),
        description: t('admin.update_failed'),
        color: 'danger',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStatusChip = (status: string) => {
    const statusConfig = {
      pending: { color: 'warning' as const, text: t('support.status_pending') },
      processing: { color: 'primary' as const, text: t('support.status_processing') },
      resolved: { color: 'success' as const, text: t('support.status_resolved') }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Chip color={config.color} size="sm" variant="flat">
        {config.text}
      </Chip>
    );
  };

  if (!ticket) return null;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="lg"
      scrollBehavior="inside"
      classNames={{
        base: "max-w-sm mx-auto",
        body: "py-4",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="solar:ticket-bold-duotone" className="text-green-500" width={20} />
            <span>{t('admin.ticket_details')}</span>
          </div>
          <div className="text-sm font-normal text-gray-500">{ticket.ticketCode}</div>
        </ModalHeader>
        
        <ModalBody>
          <div className="flex flex-col gap-4">
            {/* Ticket Info */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">{t('admin.current_status')}:</span>
                {renderStatusChip(ticket.status)}
              </div>

              <div>
                <span className="text-sm font-medium">{t('admin.user')}:</span>
                <span className="text-sm ml-2">{ticket.userReferralCode}</span>
              </div>

              <div>
                <span className="text-sm font-medium">{t('admin.wallet')}:</span>
                <span className="text-sm ml-2 font-mono">{ticket.walletAddress}</span>
              </div>
              
              {ticket.txHash && (
                <div>
                  <span className="text-sm font-medium">TX Hash:</span>
                  <span className="text-sm ml-2 font-mono break-all">{ticket.txHash}</span>
                </div>
              )}
              
              <div>
                <span className="text-sm font-medium">{t('admin.created_at')}:</span>
                <span className="text-sm ml-2">{new Date(ticket.createdAt).toLocaleString('vi-VN')}</span>
              </div>
            </div>

            <Divider />

            {/* Ticket Content */}
            <div>
              <div className="text-sm font-medium mb-2">{t('admin.ticket_content')}:</div>
              <div className="bg-gray-50 p-3 rounded-lg text-sm">
                {ticket.content}
              </div>
            </div>

            <Divider />

            {/* Response Form */}
            <div className="space-y-3">
              <Select
                label={t('admin.update_status')}
                selectedKeys={[status]}
                onSelectionChange={(keys) => setStatus(Array.from(keys)[0] as string)}
                classNames={{
                  label: "text-sm font-medium",
                }}
              >
                <SelectItem key="pending">{t('support.status_pending')}</SelectItem>
                <SelectItem key="processing">{t('support.status_processing')}</SelectItem>
                <SelectItem key="resolved">{t('support.status_resolved')}</SelectItem>
              </Select>

              <Textarea
                label={t('admin.admin_response')}
                placeholder={t('support.content_placeholder')}
                value={response}
                onChange={(e) => setResponse(e.target.value)}
                minRows={4}
                maxRows={8}
                classNames={{
                  label: "text-sm font-medium",
                }}
              />
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button
            color="danger"
            variant="light"
            onPress={onClose}
            isDisabled={isSubmitting}
          >
            {t('common.cancel')}
          </Button>
          <Button
            color="primary"
            onPress={handleSubmit}
            isLoading={isSubmitting}
          >
            {t('admin.update_ticket')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
