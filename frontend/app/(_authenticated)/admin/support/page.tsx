'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardBody, 
  Input, 
  Button, 
  Chip, 
  Select, 
  SelectItem,
  addToast,
  Spinner,
  Pagination
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useUserStore } from '@/store/userStore';
import { useTranslation } from '@/hooks/useTranslation';
import { useSupportService, Ticket } from '@/services/support.service';
import { UserRole } from '@/types/models';
import { useRouter } from 'next/navigation';
import TicketResponseModal from './components/ticket-response-modal';

export default function AdminSupportPage() {
  const router = useRouter();
  const { user } = useUserStore();
  const { t } = useTranslation();
  const supportService = useSupportService();

  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<Ticket[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [isResponseModalOpen, setIsResponseModalOpen] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Kiểm tra quyền admin
  useEffect(() => {
    if (!user || user.role !== UserRole.ADMIN) {
      router.replace('/wallet');
      return;
    }
    fetchAllTickets();
  }, [user, router]);

  const fetchAllTickets = async () => {
    setIsLoading(true);
    try {
      const response = await supportService.getAllTickets();

      if (response.success && response.data) {
        setTickets(response.data);
        setFilteredTickets(response.data);
      } else {
        addToast({
          title: t('errors.error'),
          description: typeof response.message === 'string' ? response.message : t('support.fetch_failed'),
          color: "danger",
        });
      }
    } catch (error) {
      console.error('Error fetching tickets:', error);
      addToast({
        title: t('errors.error'),
        description: t('support.fetch_failed'),
        color: "danger",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Filter tickets based on search and status
  useEffect(() => {
    let filtered = tickets;

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(ticket => 
        ticket.ticketCode.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.userReferralCode.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === statusFilter);
    }

    setFilteredTickets(filtered);
    setCurrentPage(1); // Reset to first page when filtering
  }, [searchQuery, statusFilter, tickets]);

  const renderStatusChip = (status: string) => {
    const statusConfig = {
      pending: { color: 'warning' as const, text: t('support.status_pending') },
      processing: { color: 'primary' as const, text: t('support.status_processing') },
      resolved: { color: 'success' as const, text: t('support.status_resolved') }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Chip color={config.color} size="sm" variant="flat">
        {config.text}
      </Chip>
    );
  };

  const handleTicketClick = (ticket: Ticket) => {
    setSelectedTicket(ticket);
    setIsResponseModalOpen(true);
  };

  const handleTicketUpdated = (updatedTicket: Ticket) => {
    // Cập nhật ticket trong danh sách
    setTickets(prevTickets =>
      prevTickets.map(ticket =>
        ticket.id === updatedTicket.id ? updatedTicket : ticket
      )
    );
  };

  const handleCloseModal = () => {
    setIsResponseModalOpen(false);
    setSelectedTicket(null);
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredTickets.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTickets = filteredTickets.slice(startIndex, endIndex);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <Spinner color="success" />
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex max-w-sm flex-col gap-4">
        {/* Header */}
        <div className="flex items-center gap-3 mb-2">
          <Icon icon="solar:ticket-list-bold-duotone" className="text-green-500" width={24} />
          <h2 className="text-xl font-semibold">{t('admin.ticket_management')}</h2>
        </div>

        {/* Filters */}
        <Card className="w-full">
          <CardBody className="p-4">
            <div className="flex flex-col gap-3">
              <Input
                placeholder={t('admin.search_placeholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                startContent={<Icon icon="solar:magnifer-linear" width={20} />}
                classNames={{
                  input: "text-sm",
                }}
              />

              <Select
                placeholder={t('admin.filter_by_status')}
                selectedKeys={[statusFilter]}
                onSelectionChange={(keys) => setStatusFilter(Array.from(keys)[0] as string)}
                classNames={{
                  trigger: "h-10",
                }}
              >
                <SelectItem key="all">{t('admin.all_status')}</SelectItem>
                <SelectItem key="pending">{t('support.status_pending')}</SelectItem>
                <SelectItem key="processing">{t('support.status_processing')}</SelectItem>
                <SelectItem key="resolved">{t('support.status_resolved')}</SelectItem>
              </Select>
            </div>
          </CardBody>
        </Card>

        {/* Stats */}
        <Card className="w-full">
          <CardBody className="p-4">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                {t('admin.total_tickets')}: <span className="font-semibold">{filteredTickets.length}</span> tickets
              </div>
              <Button
                size="sm"
                variant="light"
                startContent={<Icon icon="solar:refresh-linear" width={16} />}
                onPress={fetchAllTickets}
              >
                {t('admin.refresh')}
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* Tickets List */}
        {currentTickets.length > 0 ? (
          <div className="flex flex-col gap-3">
            {currentTickets.map((ticket) => (
              <Card
                key={ticket.id}
                className="w-full cursor-pointer hover:shadow-md transition-shadow"
                isPressable
                onPress={() => handleTicketClick(ticket)}
              >
                <CardBody className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div className="font-medium text-sm">{ticket.ticketCode}</div>
                    {renderStatusChip(ticket.status)}
                  </div>

                  <div className="text-xs text-gray-500 mb-2">
                    <span className="font-medium">{t('admin.user')}:</span> {ticket.userReferralCode}
                  </div>

                  <p className="text-sm mb-2" style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}>{ticket.content}</p>

                  {ticket.txHash && (
                    <div className="text-xs text-gray-500 mb-2">
                      <span className="font-medium">TX Hash:</span>
                      <span className="font-mono ml-1">{ticket.txHash.substring(0, 20)}...</span>
                    </div>
                  )}

                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>{new Date(ticket.createdAt).toLocaleString('vi-VN')}</span>
                    <Icon icon="solar:arrow-right-linear" width={16} className="text-gray-400" />
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="w-full">
            <CardBody className="p-8 text-center">
              <Icon icon="solar:ticket-outline" width={48} className="text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">{t('admin.no_tickets')}</p>
            </CardBody>
          </Card>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-4">
            <Pagination
              total={totalPages}
              page={currentPage}
              onChange={setCurrentPage}
              color="primary"
              size="sm"
            />
          </div>
        )}

        {/* Ticket Response Modal */}
        <TicketResponseModal
          isOpen={isResponseModalOpen}
          onClose={handleCloseModal}
          ticket={selectedTicket}
          onTicketUpdated={handleTicketUpdated}
        />
      </div>
    </div>
  );
}
