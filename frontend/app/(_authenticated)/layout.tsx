'use client';

import TabBar, { TabItem } from "@/components/tab-bar";
import TopBar from "@/components/top-bar";
import { useTranslation } from "@/hooks/useTranslation";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useUserStore } from "@/store/userStore";
import { UserRole } from "@/types/models";

export default function AuthenticatedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { t } = useTranslation();
  const { user } = useUserStore();
  const [isOpen, setIsOpen] = useState(true);

  const tabItems: TabItem[] = [
    { key: "/wallet", title: t('common.wallet'), icon: "solar:wallet-outline" },
    { key: "/nfts", title: t('nfts.title'), icon: "solar:gift-outline" },
    { key: "/flash-buy", title: t('flash_buy.title'), icon: "solar:magic-stick-3-outline" },
    { key: "/affiliate", title: t('account.affiliate'), icon: "solar:share-circle-outline" },
    { key: "/account", title: t('common.account'), icon: "solar:user-outline" },
    // Thêm tab admin nếu user có role ADMIN
    ...(user?.role === UserRole.ADMIN ? [
      { key: "/admin/support", title: "Admin", icon: "solar:shield-user-outline" }
    ] : []),
  ];

  return (
    <>
      <div className="bg-background max-w-sm mx-auto min-h-screen flex flex-col">
        <header className="fixed bg-background top-0 z-10 w-full max-w-sm">
          <TopBar logoText="World Mall" buttonText="0xc18e...bd0ef" />
        </header>

        <main
          className="flex-1 overflow-y-auto p-4 android-safe-container"
          style={{
            height: 'calc(100vh - 106px)',
            marginTop: '50px',
            marginBottom: '56px',
          }}
        >
          {children}
        </main>

        <footer className="fixed bottom-0 z-10 w-full max-w-sm">
          <div className="py-0">
            <TabBar items={tabItems} />
          </div>
        </footer>
      </div>

      {/* Announcement Popup */}
      <Modal
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        backdrop="blur"
        placement="center"
        classNames={{
          base: "max-w-[360px] mx-auto",
          wrapper: "h-[calc(100vh-32px)] px-4",
          body: "max-h-[calc(100vh-200px)] overflow-y-auto",
        }}
        motionProps={{
          variants: {
            enter: {
              y: 0,
              opacity: 1,
              transition: {
                duration: 0.3,
                ease: "easeOut",
              },
            },
            exit: {
              y: 20,
              opacity: 0,
              transition: {
                duration: 0.2,
                ease: "easeIn",
              },
            },
          }
        }}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex items-center justify-between sticky top-0 bg-background z-10">
                <span className="text-lg font-semibold">🎉 Special Announcement</span>
                <button
                  onClick={onClose}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <Icon icon="solar:close-circle-bold" className="w-6 h-6" />
                </button>
              </ModalHeader>

              <ModalBody className="px-4 py-2">
                <div className="space-y-4 text-foreground">
                  <p className="font-semibold text-lg">
                    Boost Your Earnings with WORLDMALL LIGHTING PACKAGE!
                  </p>
                  
                  <p>
                    Exciting news for our community! WORLDMALL is supercharging rewards with X2 mining speed, making it easier than ever to maximize your gains!
                  </p>

                  <p className="font-semibold">
                    Visit WORLDMALL D'app to take your slot
                  </p>

                  <div className="space-y-2">
                    <p className="font-semibold">Exclusive Benefits:</p>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>
                        <span className="font-semibold">$5 WM Giveaway</span> – Eligible accounts that have completed KYC will receive $WM directly, ensuring a seamless experience for our dedicated users.
                      </li>
                      <li>
                        <span className="font-semibold">Commission Rewards</span> – Earn $0.5 x 10 times, giving you more opportunities to stack up profits!
                      </li>
                    </ul>
                  </div>

                  <p className="font-semibold text-primary">
                    Don't miss out—claim your rewards and make the most of these exclusive benefits!
                  </p>
                </div>
              </ModalBody>

              <ModalFooter className="sticky bottom-0 bg-background border-t border-default-200/50">
                <Button
                  className="w-full bg-gradient-to-r from-blue-500 to-green-500 text-white"
                  onPress={onClose}
                >
                  Got it, thanks!
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}

